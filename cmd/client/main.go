package main

import (
	"bufio"
	"flag"
	"fmt"
	"io"
	"log"
	"net"
	"os"
	"os/signal"
	"socks5-quic-server/internal/client/quic"
	"strconv"
	"strings"
	"syscall"
	"time"
)

const (
	appName    = "SOCKS5-QUIC-Client"
	appVersion = "v1.0.0"
	appDesc    = "SOCKS5 over QUIC 客户端"
)

// 命令行参数
var (
	// serverAddr = flag.String("server", "develop-test-quic.apiwss1.com:8443", "服务器地址")
	serverAddr = flag.String("server", "69.164.201.159:8443", "服务器地址")
	// serverAddr    = flag.String("server", "127.0.0.1:8443", "服务器地址")
	token         = flag.String("token", "eyJ0eXAiOiJqd3QifQ.eyJzdWIiOiIxIiwiaXNzIjoiaHR0cDpcL1wvOiIsImV4cCI6MTc1MTYyMDU3NCwiaWF0IjoxNzUxMDE1Nzc0LCJuYmYiOjE3NTEwMTU3NzQsInVpZCI6MSwicyI6IlhQVEsyZSIsImp0aSI6ImNhMDI5OGNmYTgzNjlmOTNlNjU2NzMxNTQyYzQ3OTgyIn0.JDJ5JDEwJExRMWhlaUtONEc4dE51MnU1Z21NSU9NaHRiTGl0YmJoMS5UMkExejdFeVZWakpYSVFxan", "认证Token")
	clientID      = flag.String("client-id", "test-client", "客户端标识")
	localPort     = flag.Int("local-port", 1080, "本地SOCKS5监听端口")
	skipTLSVerify = flag.Bool("skip-tls-verify", true, "跳过TLS证书验证")
	debug         = flag.Bool("debug", false, "启用调试模式")
	version       = flag.Bool("version", false, "显示版本信息")
	help          = flag.Bool("help", false, "显示帮助信息")
	interactive   = flag.Bool("interactive", false, "交互式模式（手动输入目标地址）")
)

// 全局连接池
var globalConnPool *quic.ConnectionPool

func main() {
	flag.Parse()

	// 显示版本信息
	if *version {
		fmt.Printf("%s %s\n", appName, appVersion)
		fmt.Printf("%s\n", appDesc)
		return
	}

	// 显示帮助信息
	if *help {
		printUsage()
		return
	}

	printBanner()

	// 初始化全局连接池
	globalConnPool = quic.NewConnectionPool(*serverAddr, *token, *clientID, *skipTLSVerify, *debug)
	defer globalConnPool.Close()

	if *interactive {
		// 交互式模式
		runInteractiveMode()
	} else {
		// SOCKS5代理模式
		runProxyMode()
	}
}

// runInteractiveMode 运行交互式模式
func runInteractiveMode() {
	log.Printf("🚀 启动交互式模式...")

	// 预热连接池（建立连接并认证）
	_, err := globalConnPool.GetConnection()
	if err != nil {
		log.Fatalf("❌ 连接服务器失败: %v", err)
	}

	log.Printf("✅ 成功连接到服务器: %s", *serverAddr)
	log.Printf("✅ 认证成功")

	// 交互式连接测试
	scanner := bufio.NewScanner(os.Stdin)
	for {
		fmt.Print("\n请输入目标地址 (格式: host:port, 输入 'quit' 退出): ")
		if !scanner.Scan() {
			break
		}

		input := strings.TrimSpace(scanner.Text())
		if input == "quit" || input == "exit" {
			break
		}

		if input == "" {
			continue
		}

		// 解析地址
		host, portStr, err := net.SplitHostPort(input)
		if err != nil {
			fmt.Printf("❌ 地址格式错误: %v\n", err)
			continue
		}

		port, err := strconv.Atoi(portStr)
		if err != nil {
			fmt.Printf("❌ 端口格式错误: %v\n", err)
			continue
		}

		// 测试连接（使用连接池）
		if err := testConnectionWithPool(host, uint32(port)); err != nil {
			fmt.Printf("❌ 连接测试失败: %v\n", err)
		} else {
			fmt.Printf("✅ 连接测试成功: %s\n", input)
		}
	}

	log.Printf("👋 退出交互式模式")
}

// runProxyMode 运行SOCKS5代理模式
func runProxyMode() {
	log.Printf("🚀 启动SOCKS5代理模式，监听端口: %d", *localPort)

	// 监听本地端口
	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", *localPort))
	if err != nil {
		log.Fatalf("❌ 监听端口失败: %v", err)
	}
	defer listener.Close()

	log.Printf("✅ SOCKS5代理服务启动，监听地址: %s", listener.Addr())
	log.Printf("💡 现在可以将浏览器或其他应用的SOCKS5代理设置为: 127.0.0.1:%d", *localPort)

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 处理连接
	go func() {
		for {
			clientConn, err := listener.Accept()
			if err != nil {
				log.Printf("❌ 接受连接失败: %v", err)
				continue
			}

			go handleClientConnection(clientConn)
		}
	}()

	// 等待信号
	sig := <-sigChan
	log.Printf("🔔 收到信号: %v", sig)
	log.Printf("👋 关闭代理服务")
}

// handleClientConnection 处理客户端连接
func handleClientConnection(clientConn net.Conn) {
	defer clientConn.Close()

	if *debug {
		log.Printf("🔗 新的客户端连接: %s", clientConn.RemoteAddr())
	}

	// 处理SOCKS5握手
	targetAddr, err := handleSOCKS5Handshake(clientConn)
	if err != nil {
		log.Printf("❌ SOCKS5握手失败: %v", err)
		return
	}

	if *debug {
		log.Printf("🎯 目标地址: %s", targetAddr)
	}

	// 解析目标地址
	host, portStr, err := net.SplitHostPort(targetAddr)
	if err != nil {
		log.Printf("❌ 解析目标地址失败: %v", err)
		return
	}

	port, err := strconv.Atoi(portStr)
	if err != nil {
		log.Printf("❌ 解析端口失败: %v", err)
		return
	}

	// 使用连接池创建到目标的连接
	stream, err := globalConnPool.CreateStream(host, uint32(port))
	if err != nil {
		log.Printf("❌ 创建目标连接失败: %v", err)
		return
	}
	defer stream.Close()

	if *debug {
		log.Printf("✅ 成功建立代理连接: %s -> %s", clientConn.RemoteAddr(), targetAddr)
	}

	// 双向数据转发
	go func() {
		defer stream.Close()
		defer clientConn.Close()
		io.Copy(stream, clientConn)
	}()

	io.Copy(clientConn, stream)

	if *debug {
		log.Printf("🔚 代理连接结束: %s -> %s", clientConn.RemoteAddr(), targetAddr)
	}
}

// handleSOCKS5Handshake 处理SOCKS5握手
func handleSOCKS5Handshake(conn net.Conn) (string, error) {
	// 读取方法选择请求
	buf := make([]byte, 256)
	n, err := conn.Read(buf)
	if err != nil {
		return "", err
	}

	if n < 3 || buf[0] != 0x05 {
		return "", fmt.Errorf("不是有效的SOCKS5请求")
	}

	// 发送方法选择响应（无认证）
	_, err = conn.Write([]byte{0x05, 0x00})
	if err != nil {
		return "", err
	}

	// 读取连接请求
	n, err = conn.Read(buf)
	if err != nil {
		return "", err
	}

	if n < 7 || buf[0] != 0x05 || buf[1] != 0x01 {
		return "", fmt.Errorf("无效的SOCKS5连接请求")
	}

	// 解析目标地址
	var targetAddr string
	addrType := buf[3]
	switch addrType {
	case 0x01: // IPv4
		if n < 10 {
			return "", fmt.Errorf("IPv4地址数据不完整")
		}
		ip := net.IP(buf[4:8])
		port := uint16(buf[8])<<8 + uint16(buf[9])
		targetAddr = fmt.Sprintf("%s:%d", ip.String(), port)
	case 0x03: // 域名
		if n < 5 {
			return "", fmt.Errorf("域名地址数据不完整")
		}
		domainLen := int(buf[4])
		if n < 5+domainLen+2 {
			return "", fmt.Errorf("域名地址数据不完整")
		}
		domain := string(buf[5 : 5+domainLen])
		port := uint16(buf[5+domainLen])<<8 + uint16(buf[5+domainLen+1])
		targetAddr = fmt.Sprintf("%s:%d", domain, port)
	case 0x04: // IPv6
		if n < 22 {
			return "", fmt.Errorf("IPv6地址数据不完整")
		}
		ip := net.IP(buf[4:20])
		port := uint16(buf[20])<<8 + uint16(buf[21])
		targetAddr = fmt.Sprintf("[%s]:%d", ip.String(), port)
	default:
		return "", fmt.Errorf("不支持的地址类型: %d", addrType)
	}

	// 发送连接响应
	response := []byte{0x05, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}
	_, err = conn.Write(response)
	if err != nil {
		return "", err
	}

	return targetAddr, nil
}

// testConnectionWithPool 使用连接池测试连接
func testConnectionWithPool(host string, port uint32) error {
	stream, err := globalConnPool.CreateStream(host, port)
	if err != nil {
		return err
	}
	defer stream.Close()

	// 发送简单的HTTP GET请求（如果是HTTP端口）
	if port == 80 || port == 8080 {
		request := fmt.Sprintf("GET / HTTP/1.1\r\nHost: %s\r\nConnection: close\r\n\r\n", host)
		_, err = stream.Write([]byte(request))
		if err != nil {
			return fmt.Errorf("发送HTTP请求失败: %v", err)
		}

		// 读取响应
		buf := make([]byte, 1024)
		stream.SetReadDeadline(time.Now().Add(5 * time.Second))
		n, err := stream.Read(buf)
		if err != nil && err != io.EOF {
			return fmt.Errorf("读取HTTP响应失败: %v", err)
		}

		fmt.Printf("📄 HTTP响应 (%d bytes):\n%s\n", n, string(buf[:n]))
	}

	return nil
}

// printBanner 打印启动横幅
func printBanner() {
	fmt.Println("╔══════════════════════════════════════════════════════════════╗")
	fmt.Printf("║  %-58s  ║\n", appName+" "+appVersion)
	fmt.Printf("║  %-58s  ║\n", appDesc)
	fmt.Println("║                                                              ║")
	fmt.Printf("║  服务器地址: %-47s  ║\n", *serverAddr)
	fmt.Printf("║  认证Token: %-48s  ║\n", maskToken(*token))
	fmt.Printf("║  客户端ID: %-49s  ║\n", *clientID)
	if !*interactive {
		fmt.Printf("║  本地端口: %-49d  ║\n", *localPort)
	}
	fmt.Printf("║  跳过TLS验证: %-44t  ║\n", *skipTLSVerify)
	fmt.Printf("║  调试模式: %-49t  ║\n", *debug)
	fmt.Println("╚══════════════════════════════════════════════════════════════╝")
	fmt.Println()
}

// printUsage 打印使用说明
func printUsage() {
	fmt.Printf("%s %s - %s\n\n", appName, appVersion, appDesc)
	fmt.Println("使用方法:")
	fmt.Printf("  %s [选项]\n\n", os.Args[0])
	fmt.Println("选项:")
	flag.PrintDefaults()
	fmt.Println("\n示例:")
	fmt.Printf("  # 启动SOCKS5代理模式\n")
	fmt.Printf("  %s -server localhost:8443 -token your-token\n", os.Args[0])
	fmt.Printf("\n  # 启动交互式测试模式\n")
	fmt.Printf("  %s -server localhost:8443 -token your-token -interactive\n", os.Args[0])
	fmt.Printf("\n  # 自定义本地端口\n")
	fmt.Printf("  %s -local-port 1081 -token your-token\n", os.Args[0])
}

// maskToken 掩码Token显示
func maskToken(token string) string {
	if len(token) <= 8 {
		return "***"
	}
	return token[:4] + "***" + token[len(token)-4:]
}
