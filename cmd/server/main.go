package main

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/pem"
	"flag"
	"fmt"
	"log"
	"math/big"
	"net"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"socks5-quic-server/internal/server"

	"golang.org/x/crypto/acme/autocert"
)

const (
	// 默认配置值
	defaultQuicListenAddr  = ":8443"
	defaultHttp2ListenAddr = ":8443"
	defaultMaxConnections  = 1000
	defaultIdleTimeout     = 1 * time.Minute
	defaultCleanupInterval = 1 * time.Minute
	defaultStatsInterval   = 5 * time.Minute
)

// 命令行参数
var (
	listenAddr                = flag.String("listen", defaultQuicListenAddr, "监听地址 (例如: :8444 或 0.0.0.0:8444)")
	http2ListenAddr           = flag.String("http2-listen", defaultHttp2ListenAddr, "HTTP2监听地址 (例如: :8443)")
	maxConnections            = flag.Int("max-conn", defaultMaxConnections, "最大并发连接数")
	idleTimeout               = flag.Duration("idle-timeout", defaultIdleTimeout, "连接空闲超时时间")
	cleanupInterval           = flag.Duration("cleanup-interval", defaultCleanupInterval, "连接清理间隔")
	statsInterval             = flag.Duration("stats-interval", defaultStatsInterval, "统计信息输出间隔")
	certFile                  = flag.String("cert", "", "TLS证书文件 (留空则自动生成自签名证书)")
	keyFile                   = flag.String("key", "", "TLS私钥文件 (留空则自动生成自签名证书)")
	domain                    = flag.String("domain", "", "域名 (启用Let's Encrypt自动证书)")
	autoCert                  = flag.Bool("auto-cert", false, "启用Let's Encrypt自动证书管理")
	certCacheDir              = flag.String("cert-cache-dir", "./certs", "证书缓存目录")
	httpPort                  = flag.String("http-port", ":80", "HTTP挑战端口 (Let's Encrypt验证使用)")
	authMode                  = flag.String("auth-mode", "api", "认证模式: token, api, hybrid")
	authAPI                   = flag.String("auth-api", "http://192.168.0.106:29501", "API认证服务URL")
	authKey                   = flag.String("auth-key", "ewefjlw---ejfl.0.,,,,,,", "API认证密钥")
	enableQuic                = flag.Bool("enable-quic", true, "启用QUIC服务")
	enableHttp2               = flag.Bool("enable-http2", true, "启用HTTP2服务")
	debug                     = flag.Bool("debug", false, "启用调试模式（详细事件日志）")
	version                   = flag.Bool("version", false, "显示版本信息")
	help                      = flag.Bool("help", false, "显示帮助信息")
	userTrafficReportInterval = flag.Duration("user-traffic-report-interval", 1*time.Minute, "用户流量报告间隔")
)

const (
	appName    = "SOCKS5-QUIC-Server"
	appVersion = "v1.0.0"
	appDesc    = "基于QUIC协议的高性能SOCKS5代理服务器"
)

func main() {
	flag.Parse()

	// 显示版本信息
	if *version {
		fmt.Printf("%s %s\n", appName, appVersion)
		fmt.Printf("%s\n", appDesc)
		fmt.Printf("构建时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
		return
	}

	// 显示帮助信息
	if *help {
		printUsage()
		return
	}

	// 打印启动横幅
	printBanner()

	// 验证配置参数
	if err := validateConfig(); err != nil {
		log.Fatalf("❌ 配置验证失败: %v", err)
	}

	// 创建TLS配置
	tlsConfig, err := createTLSConfig()
	if err != nil {
		log.Fatalf("❌ 创建TLS配置失败: %v", err)
	}

	// 运行服务器
	runServer(tlsConfig)

	log.Printf("👋 再见！")
}

// runServer 运行服务器
func runServer(tlsConfig *tls.Config) {
	// 创建认证配置
	authConfig := &server.AuthConfig{
		Mode:                      *authMode,
		APIUrl:                    *authAPI,
		APIKey:                    *authKey,
		Timeout:                   10,
		CacheSize:                 1000,
		CacheTTL:                  300,
		UserTrafficReportInterval: *userTrafficReportInterval,
	}

	// 创建多协议服务器配置
	config := &server.MultiProtocolConfig{
		MaxConnections:  *maxConnections,
		IdleTimeout:     *idleTimeout,
		CleanupInterval: *cleanupInterval,
		StatsInterval:   *statsInterval,
		AuthConfig:      authConfig,
		EnableDebug:     *debug,

		// QUIC配置
		QUICEnabled:    *enableQuic,
		QUICListenAddr: *listenAddr,
		QUICTLSConfig:  tlsConfig,

		// HTTP2配置
		HTTP2Enabled:    *enableHttp2,
		HTTP2ListenAddr: *http2ListenAddr,
		HTTP2TLSConfig:  tlsConfig,
		HTTP2MaxStreams: 20,
	}

	// 创建多协议服务器实例
	srv, err := server.NewMultiProtocolServer(config)
	if err != nil {
		log.Fatalf("❌ 创建多协议服务器失败: %v", err)
	}

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM, syscall.SIGHUP)

	// 启动服务器
	if err := srv.Start(); err != nil {
		log.Fatalf("❌ 启动多协议服务器失败: %v", err)
	}

	var protocolInfo string
	if *enableQuic && *enableHttp2 {
		protocolInfo = "QUIC + HTTP2"
	} else if *enableQuic {
		protocolInfo = "QUIC"
	} else if *enableHttp2 {
		protocolInfo = "HTTP2"
	}

	log.Printf("🎉 %s SOCKS5服务器启动完成，按 Ctrl+C 停止服务器", protocolInfo)

	// 等待信号
	sig := <-sigChan
	log.Printf("🔔 收到信号: %v", sig)

	// 优雅关闭服务器
	log.Printf("🔄 正在优雅关闭服务器...")
	if err := srv.Stop(); err != nil {
		log.Printf("❌ 关闭服务器时出错: %v", err)
	}
}

// printBanner 打印启动横幅
func printBanner() {
	fmt.Println("╔══════════════════════════════════════════════════════════════╗")
	fmt.Printf("║  %-58s  ║\n", appName+" "+appVersion)
	fmt.Printf("║  %-58s  ║\n", appDesc)
	fmt.Println("║                                                              ║")
	fmt.Printf("║  QUIC地址: %-47s  ║\n", *listenAddr)
	fmt.Printf("║  HTTP2地址: %-46s  ║\n", *http2ListenAddr)
	fmt.Printf("║  最大连接: %-47d  ║\n", *maxConnections)
	fmt.Printf("║  空闲超时: %-47s  ║\n", *idleTimeout)
	fmt.Printf("║  清理间隔: %-47s  ║\n", *cleanupInterval)
	fmt.Printf("║  统计间隔: %-47s  ║\n", *statsInterval)
	fmt.Printf("║  认证模式: %-47s  ║\n", *authMode)

	// 证书相关信息
	if *autoCert {
		fmt.Printf("║  自动证书: %-47s  ║\n", "Let's Encrypt")
		fmt.Printf("║  域名: %-51s  ║\n", *domain)
		fmt.Printf("║  证书缓存: %-47s  ║\n", *certCacheDir)
		fmt.Printf("║  HTTP端口: %-47s  ║\n", *httpPort)
	} else if *certFile != "" {
		fmt.Printf("║  证书文件: %-47s  ║\n", *certFile)
	} else {
		fmt.Printf("║  证书模式: %-47s  ║\n", "自签名证书")
	}

	fmt.Printf("║  启用QUIC: %-46t  ║\n", *enableQuic)
	fmt.Printf("║  启用HTTP2: %-45t  ║\n", *enableHttp2)
	fmt.Printf("║  调试模式: %-47t  ║\n", *debug)
	fmt.Println("╚══════════════════════════════════════════════════════════════╝")
	fmt.Println()
}

// printUsage 打印使用说明
func printUsage() {
	fmt.Printf("%s %s - %s\n\n", appName, appVersion, appDesc)
	fmt.Println("使用方法:")
	fmt.Printf("  %s [选项]\n\n", os.Args[0])
	fmt.Println("选项:")
	flag.PrintDefaults()
	fmt.Println("\n示例:")
	fmt.Printf("  # 使用Let's Encrypt自动证书\n")
	fmt.Printf("  %s -domain yourdomain.com -auto-cert\n", os.Args[0])
	fmt.Printf("  %s -domain yourdomain.com -auto-cert -cert-cache-dir /etc/ssl/certs\n", os.Args[0])
	fmt.Printf("\n  # 使用手动证书\n")
	fmt.Printf("  %s -cert server.crt -key server.key\n", os.Args[0])
	fmt.Printf("\n  # 其他配置示例\n")
	fmt.Printf("  %s -listen :8443 -max-conn 500\n", os.Args[0])
	fmt.Printf("  %s -idle-timeout 10m -stats-interval 1m\n", os.Args[0])
	fmt.Printf("  %s -auth-mode api -auth-api http://localhost:8080/auth\n", os.Args[0])
	fmt.Printf("\n注意事项:\n")
	fmt.Printf("  - 使用Let's Encrypt时需要确保域名指向服务器IP\n")
	fmt.Printf("  - HTTP端口(默认80)用于域名验证，需要可从公网访问\n")
	fmt.Printf("  - 证书会自动续期，无需手动维护\n")
}

// validateConfig 验证配置参数
func validateConfig() error {
	// 检查是否至少启用一个协议
	if !*enableQuic && !*enableHttp2 {
		return fmt.Errorf("至少需要启用一个协议（QUIC或HTTP2）")
	}

	if *maxConnections <= 0 {
		return fmt.Errorf("最大连接数必须大于0")
	}

	if *idleTimeout <= 0 {
		return fmt.Errorf("空闲超时时间必须大于0")
	}

	if *cleanupInterval <= 0 {
		return fmt.Errorf("清理间隔必须大于0")
	}

	if *statsInterval <= 0 {
		return fmt.Errorf("统计间隔必须大于0")
	}

	// 检查自动证书配置
	if *autoCert {
		if *domain == "" {
			return fmt.Errorf("启用自动证书时必须指定域名")
		}
		if *certFile != "" || *keyFile != "" {
			return fmt.Errorf("启用自动证书时不能同时指定证书文件")
		}
		// 验证域名格式
		if strings.Contains(*domain, "localhost") || strings.Contains(*domain, "127.0.0.1") {
			return fmt.Errorf("Let's Encrypt不支持localhost或本地IP地址")
		}
	}

	// 检查证书和密钥文件（非自动证书模式）
	if !*autoCert {
		if (*certFile != "" && *keyFile == "") || (*certFile == "" && *keyFile != "") {
			return fmt.Errorf("证书文件和密钥文件必须同时指定或同时留空")
		}

		if *certFile != "" {
			if _, err := os.Stat(*certFile); os.IsNotExist(err) {
				return fmt.Errorf("证书文件不存在: %s", *certFile)
			}
		}

		if *keyFile != "" {
			if _, err := os.Stat(*keyFile); os.IsNotExist(err) {
				return fmt.Errorf("密钥文件不存在: %s", *keyFile)
			}
		}
	}

	// 检查认证配置
	if *authMode != "token" && *authMode != "api" && *authMode != "hybrid" {
		return fmt.Errorf("认证模式必须是token, api, hybrid")
	}

	return nil
}

// createTLSConfig 创建TLS配置
func createTLSConfig() (*tls.Config, error) {
	if *autoCert {
		// 使用Let's Encrypt自动证书
		return createAutoCertTLSConfig()
	}

	var cert tls.Certificate
	var err error

	if *certFile != "" && *keyFile != "" {
		// 使用指定的证书和密钥文件
		log.Printf("🔐 使用指定的TLS证书: %s", *certFile)
		cert, err = tls.LoadX509KeyPair(*certFile, *keyFile)
		if err != nil {
			return nil, fmt.Errorf("加载TLS证书失败: %w", err)
		}
	} else {
		// 生成自签名证书
		log.Printf("🔐 生成自签名TLS证书...")
		cert, err = generateSelfSignedCert()
		if err != nil {
			return nil, fmt.Errorf("生成自签名证书失败: %w", err)
		}
	}

	return &tls.Config{
		Certificates: []tls.Certificate{cert},
		NextProtos:   []string{"socks5-quic", "h2"}, // SOCKS5 over QUIC 协议标识
		MinVersion:   tls.VersionTLS12,
		CipherSuites: []uint16{
			tls.TLS_AES_128_GCM_SHA256,
			tls.TLS_AES_256_GCM_SHA384,
			tls.TLS_CHACHA20_POLY1305_SHA256,
			tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
			tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,
		},
		// 启用会话票据以支持0-RTT
		SessionTicketsDisabled: false,
	}, nil
}

// createAutoCertTLSConfig 创建自动证书TLS配置
func createAutoCertTLSConfig() (*tls.Config, error) {
	log.Printf("🔐 启用Let's Encrypt自动证书管理: %s", *domain)

	// 创建证书缓存目录
	if err := os.MkdirAll(*certCacheDir, 0700); err != nil {
		return nil, fmt.Errorf("创建证书缓存目录失败: %w", err)
	}

	// 创建autocert管理器
	certManager := &autocert.Manager{
		Prompt:     autocert.AcceptTOS,
		HostPolicy: autocert.HostWhitelist(*domain),
		Cache:      autocert.DirCache(*certCacheDir),
	}

	// 启动HTTP挑战服务器
	go startHTTPChallengeServer(certManager)

	// 创建TLS配置
	tlsConfig := &tls.Config{
		GetCertificate: certManager.GetCertificate,
		NextProtos:     []string{"socks5-quic", "h2"},
		MinVersion:     tls.VersionTLS12,
		CipherSuites: []uint16{
			tls.TLS_AES_128_GCM_SHA256,
			tls.TLS_AES_256_GCM_SHA384,
			tls.TLS_CHACHA20_POLY1305_SHA256,
			tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
			tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,
		},
		SessionTicketsDisabled: false,
	}

	log.Printf("✅ Let's Encrypt自动证书配置完成")
	return tlsConfig, nil
}

// startHTTPChallengeServer 启动HTTP挑战服务器
func startHTTPChallengeServer(certManager *autocert.Manager) {
	log.Printf("🌐 启动HTTP挑战服务器: %s", *httpPort)

	server := &http.Server{
		Addr:    *httpPort,
		Handler: certManager.HTTPHandler(nil),
	}

	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Printf("❌ HTTP挑战服务器错误: %v", err)
	}
}

// generateSelfSignedCert 生成自签名证书
func generateSelfSignedCert() (tls.Certificate, error) {
	// 生成RSA私钥
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return tls.Certificate{}, fmt.Errorf("生成RSA私钥失败: %w", err)
	}

	// 创建证书模板
	template := x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			Organization:  []string{"SOCKS5-QUIC-Server"},
			Country:       []string{"CN"},
			Province:      []string{""},
			Locality:      []string{""},
			StreetAddress: []string{""},
			PostalCode:    []string{""},
		},
		NotBefore:             time.Now(),
		NotAfter:              time.Now().Add(365 * 24 * time.Hour), // 1年有效期
		KeyUsage:              x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
		BasicConstraintsValid: true,
		DNSNames:              []string{"localhost", "*.localhost"},
		IPAddresses:           []net.IP{net.IPv4(127, 0, 0, 1), net.IPv6loopback},
	}

	// 生成证书
	certDER, err := x509.CreateCertificate(rand.Reader, &template, &template, &privateKey.PublicKey, privateKey)
	if err != nil {
		return tls.Certificate{}, fmt.Errorf("创建证书失败: %w", err)
	}

	// 编码证书和私钥
	certPEM := pem.EncodeToMemory(&pem.Block{Type: "CERTIFICATE", Bytes: certDER})
	keyPEM := pem.EncodeToMemory(&pem.Block{Type: "RSA PRIVATE KEY", Bytes: x509.MarshalPKCS1PrivateKey(privateKey)})

	// 创建TLS证书
	cert, err := tls.X509KeyPair(certPEM, keyPEM)
	if err != nil {
		return tls.Certificate{}, fmt.Errorf("创建TLS证书失败: %w", err)
	}

	return cert, nil
}
