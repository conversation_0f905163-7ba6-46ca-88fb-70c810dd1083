package http2

import (
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net"
	"net/http"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"socks5-quic-server/internal/client"
	"socks5-quic-server/pkg/logger"

	"golang.org/x/net/http2"
)

// Http2Client HTTP/2客户端实现 - 多TCP并行 + 自动重试版
type Http2Client struct {
	// 连接池相关
	transports []*http.Transport
	rrCounter  uint32 // 轮询计数器

	// 服务器信息
	node *client.ServerNode

	// 流量统计
	uploadBytes   int64
	downloadBytes int64

	// 连接状态
	isConnected int32

	// 同步控制
	mu     sync.RWMutex
	ctx    context.Context
	cancel context.CancelFunc
}

// h2StreamConn HTTP/2流连接包装器
type h2StreamConn struct {
	r             io.ReadCloser
	w             io.WriteCloser
	closers       []io.Closer
	uploadBytes   *int64
	downloadBytes *int64
}

func (h *h2StreamConn) Read(b []byte) (n int, err error) {
	n, err = h.r.Read(b)
	if n > 0 {
		atomic.AddInt64(h.downloadBytes, int64(n))
	}
	return
}

func (h *h2StreamConn) Write(b []byte) (n int, err error) {
	n, err = h.w.Write(b)
	if n > 0 {
		atomic.AddInt64(h.uploadBytes, int64(n))
	}
	return
}

func (h *h2StreamConn) Close() error {
	var errs []error
	for _, closer := range h.closers {
		if err := closer.Close(); err != nil {
			errs = append(errs, err)
		}
	}
	if len(errs) > 0 {
		return fmt.Errorf("close errors: %v", errs)
	}
	return nil
}

func (h *h2StreamConn) LocalAddr() net.Addr                { return dummyAddr("h2-local") }
func (h *h2StreamConn) RemoteAddr() net.Addr               { return dummyAddr("h2-remote") }
func (h *h2StreamConn) SetDeadline(t time.Time) error      { return nil }
func (h *h2StreamConn) SetReadDeadline(t time.Time) error  { return nil }
func (h *h2StreamConn) SetWriteDeadline(t time.Time) error { return nil }

type dummyAddr string

func (d dummyAddr) Network() string { return string(d) }
func (d dummyAddr) String() string  { return string(d) }

// NewHttp2Client 创建新的HTTP/2客户端
func NewHttp2Client(node *client.ServerNode) *Http2Client {
	ctx, cancel := context.WithCancel(context.Background())
	return &Http2Client{
		ctx:    ctx,
		cancel: cancel,
		node:   node,
	}
}

// pickTransport 轮询选择传输层
func (c *Http2Client) pickTransport() *http.Transport {
	if len(c.transports) == 0 {
		return nil
	}
	idx := atomic.AddUint32(&c.rrCounter, 1)
	return c.transports[int(idx)%len(c.transports)]
}

// dial 将SOCKS5 CONNECT转换为HTTP/2流
func (c *Http2Client) dial(ctx context.Context, network string, target string) (net.Conn, error) {
	transport := c.pickTransport()
	if transport == nil {
		return nil, fmt.Errorf("no available transport")
	}

	cli := &http.Client{Transport: transport}

	pr, pw := io.Pipe()
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, "https://"+c.node.GetServerAddr()+"/tunnel", pr)
	if err != nil {
		pw.Close()
		return nil, err
	}

	// 设置目标地址和认证头
	req.Header.Set("x-target", target)
	//req.Header.Set("x-auth-token", fmt.Sprintf("Bearer %s", c.node.Token))
	req.Header.Set("x-auth-token", c.node.Token)
	fmt.Println("x-auth-token", c.node.Token)

	// 允许Transport在自动重试时重放body
	req.GetBody = func() (io.ReadCloser, error) {
		return io.NopCloser(strings.NewReader("")), nil
	}

	// 自动重试机制（最多3次）
	for attempt := 0; attempt < 3; attempt++ {
		resp, err := cli.Do(req)
		if err != nil {
			pw.Close()
			return nil, err
		}

		switch resp.StatusCode {
		case http.StatusOK:
			// 成功 - 将响应体和管道写入器绑定为net.Conn
			return &h2StreamConn{
				r:             resp.Body,
				w:             pw,
				closers:       []io.Closer{resp.Body, pw},
				uploadBytes:   &c.uploadBytes,
				downloadBytes: &c.downloadBytes,
			}, nil
		case http.StatusNoContent:
			// 204探测，关闭并在新流上重试
			resp.Body.Close()
			continue
		default:
			pw.Close()
			msg := fmt.Errorf("unexpected status %s", resp.Status)
			resp.Body.Close()
			return nil, msg
		}
	}
	pw.Close()
	return nil, fmt.Errorf("connect attempts exceeded")
}

// GetUploadBytes 获取上传字节数
func (c *Http2Client) GetUploadBytes() int64 {
	return atomic.LoadInt64(&c.uploadBytes)
}

// GetDownloadBytes 获取下载字节数
func (c *Http2Client) GetDownloadBytes() int64 {
	return atomic.LoadInt64(&c.downloadBytes)
}

// ResetTrafficStats 重置流量统计
func (c *Http2Client) ResetTrafficStats() int {
	atomic.StoreInt64(&c.uploadBytes, 0)
	atomic.StoreInt64(&c.downloadBytes, 0)
	return 0 // 成功
}

// Disconnect 断开连接
func (c *Http2Client) Disconnect() int {
	c.mu.Lock()
	defer c.mu.Unlock()

	if atomic.LoadInt32(&c.isConnected) == 0 {
		return 0 // 已经断开
	}

	// 关闭所有传输层连接
	for _, transport := range c.transports {
		if transport != nil {
			transport.CloseIdleConnections()
		}
	}
	c.transports = nil

	atomic.StoreInt32(&c.isConnected, 0)

	// 取消上下文
	if c.cancel != nil {
		c.cancel()
	}

	return 0 // 成功
}

// IsConnected 检查是否已连接
func (c *Http2Client) IsConnected() int {
	return int(atomic.LoadInt32(&c.isConnected))
}

// Connect 连接到服务器
func (c *Http2Client) Connect() int {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 如果已经连接，先断开
	if atomic.LoadInt32(&c.isConnected) == 1 {
		c.disconnectUnsafe()
	}

	// 创建新的上下文
	c.ctx, c.cancel = context.WithCancel(context.Background())

	// 获取连接数配置，默认为4
	connCount := 4
	if conns, exists := c.node.GetQueryParam("conns"); exists {
		if parsed, err := fmt.Sscanf(conns, "%d", &connCount); err != nil || parsed != 1 {
			connCount = 4 // 解析失败时使用默认值
		}
	}

	// 获取TLS配置
	skipTLSVerify, exists := c.node.GetQueryParam("skipTLSVerify")
	skipTLS := exists && skipTLSVerify == "1"
	fmt.Println("skipTLS", skipTLS)
	// 构建传输层连接池
	c.transports = make([]*http.Transport, connCount)
	for i := 0; i < connCount; i++ {
		tr := &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: skipTLS,
				NextProtos:         []string{"h2", "http/1.1"},
			},
			ForceAttemptHTTP2: true,
			DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
				dialer := &net.Dialer{
					Timeout:   10 * time.Second,
					KeepAlive: 30 * time.Second,
				}
				return dialer.DialContext(ctx, network, addr)
			},
		}
		// 启用HTTP/2功能
		http2.ConfigureTransport(tr)
		c.transports[i] = tr
	}

	// 测试连接
	if err := c.testConnection(); err != nil {
		fmt.Println("testConnection failed", err)
		c.disconnectUnsafe()
		return 1 // 连接失败
	}

	atomic.StoreInt32(&c.isConnected, 1)

	// 启动连接监控
	go c.monitorConnection()

	return 0 // 成功
}

// testConnection 测试连接是否可用
func (c *Http2Client) testConnection() error {
	transport := c.pickTransport()
	if transport == nil {
		return fmt.Errorf("no available transport")
	}

	cli := &http.Client{Transport: transport}

	pr, pw := io.Pipe()
	ctx, _ := context.WithTimeout(context.Background(), 6*time.Second)
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, "https://"+c.node.GetServerAddr()+"/tunnel", pr)
	if err != nil {
		pw.Close()
		return err
	}

	// 设置目标地址和认证头
	req.Header.Set("x-target", "www.google.com:443")
	//req.Header.Set("x-auth-token", fmt.Sprintf("Bearer %s", c.node.Token))
	req.Header.Set("x-auth-token", c.node.Token)
	fmt.Println("x-auth-token", c.node.Token)
	logger.Info("http2", "开始测试连接")
	// 允许Transport在自动重试时重放body
	req.GetBody = func() (io.ReadCloser, error) {
		return io.NopCloser(strings.NewReader("")), nil
	}

	// 自动重试机制（最多3次）
	for attempt := 0; attempt < 3; attempt++ {
		resp, err := cli.Do(req)
		if err != nil {
			pw.Close()
			return err
		}

		switch resp.StatusCode {
		case http.StatusOK:
			// 成功 - 将响应体和管道写入器绑定为net.Conn
			logger.Info("http2", "测试连接成功")
			return nil
		case http.StatusNoContent:
			// 204探测，关闭并在新流上重试
			resp.Body.Close()
			continue
		default:
			pw.Close()
			resp.Body.Close()
			logger.Info("http2", "测试连接失败 %s", resp.Status)
			return fmt.Errorf(resp.Status)
		}
	}
	pw.Close()
	return nil
}

// disconnectUnsafe 内部断开连接方法（不加锁）
func (c *Http2Client) disconnectUnsafe() {
	for _, transport := range c.transports {
		if transport != nil {
			transport.CloseIdleConnections()
		}
	}
	c.transports = nil
	atomic.StoreInt32(&c.isConnected, 0)

	if c.cancel != nil {
		c.cancel()
	}
}

// monitorConnection 监控连接状态
func (c *Http2Client) monitorConnection() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			if atomic.LoadInt32(&c.isConnected) == 0 {
				return
			}

			// 定期发送心跳检查连接状态
			//go c.heartbeat()
		}
	}
}

// heartbeat 发送心跳检查连接
// func (c *Http2Client) heartbeat() {
// 	if atomic.LoadInt32(&c.isConnected) == 0 {
// 		return
// 	}

// 	transport := c.pickTransport()
// 	if transport == nil {
// 		c.Disconnect()
// 		return
// 	}

// 	cli := &http.Client{Transport: transport}

// 	// 创建心跳请求
// 	req, err := http.NewRequestWithContext(c.ctx, "HEAD", c.node.GetServerAddr()+"/ping", nil)
// 	if err != nil {
// 		return
// 	}

// 	// 设置认证头
// 	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.node.Token))
// 	req.Header.Set("X-Client-ID", c.node.UniqueName)

// 	// 发送心跳
// 	resp, err := cli.Do(req)
// 	if err != nil {
// 		// 心跳失败，断开连接
// 		c.Disconnect()
// 		return
// 	}
// 	defer resp.Body.Close()

// 	// 如果心跳失败，断开连接
// 	if resp.StatusCode != http.StatusOK {
// 		c.Disconnect()
// 	}
// }

// SendRequest 发送HTTP请求（辅助方法）

// GetConnectionStats 获取连接统计信息
func (c *Http2Client) GetConnectionStats() map[string]interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()

	skipTLSVerify, exists := c.node.GetQueryParam("skipTLSVerify")
	skipTLSVerifyb := false
	if exists && skipTLSVerify == "1" {
		skipTLSVerifyb = true
	}

	connCount := len(c.transports)
	if connCount == 0 {
		connCount = 4 // 默认值
	}

	stats := map[string]interface{}{
		"server_addr":     c.node.GetServerAddr(),
		"client_id":       c.node.UniqueName,
		"connected":       atomic.LoadInt32(&c.isConnected) == 1,
		"upload_bytes":    atomic.LoadInt64(&c.uploadBytes),
		"download_bytes":  atomic.LoadInt64(&c.downloadBytes),
		"connection_pool": connCount,
		"skip_tls_verify": skipTLSVerifyb,
	}

	return stats
}

// GetClientType 获取客户端类型
func (c *Http2Client) GetClientType() string {
	return "http2"
}

// IoBridge 桥接IO - 实现完整的数据传输
func (c *Http2Client) IoBridge(conn net.Conn, dstAddr string, dstPort int) int {
	fmt.Println("IoBridge: IoBridge")
	if atomic.LoadInt32(&c.isConnected) == 0 {
		fmt.Printf("IoBridge: Client not connected")
		return -1
	}

	address := fmt.Sprintf("%s:%d", dstAddr, dstPort)
	fmt.Println("address", address, c.node.GetServerAddr())

	// 创建HTTP/2流连接
	h2Conn, err := c.dial(c.ctx, "tcp", address)
	if err != nil {
		fmt.Printf("IoBridge: Failed to create H2 stream for %s: %v", address, err)
		return -1
	}

	fmt.Printf("IoBridge: Starting data transfer for %s", address)

	// 使用defer确保连接被正确关闭
	defer func() {
		// 捕获任何panic，避免影响CGO回调
		if r := recover(); r != nil {
			fmt.Printf("IoBridge panic: %v", r)
		}
		fmt.Printf("IoBridge: Closing connections for %s", address)
		h2Conn.Close()
		conn.Close()
	}()

	var wg sync.WaitGroup
	wg.Add(2)

	// 上传方向：conn -> h2Conn
	go func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("Upload goroutine panic: %v", r)
			}
			wg.Done()
		}()
		fmt.Printf("IoBridge: Starting upload direction for %s", address)
		written, err := c.copyWithStats(h2Conn, conn, true)
		fmt.Printf("IoBridge: Upload finished for %s, written=%d, error=%v", address, written, err)
	}()

	// 下载方向：h2Conn -> conn
	go func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("Download goroutine panic: %v", r)
			}
			wg.Done()
		}()
		fmt.Printf("IoBridge: Starting download direction for %s", address)
		written, err := c.copyWithStats(conn, h2Conn, false)
		fmt.Printf("IoBridge: Download finished for %s, written=%d, error=%v", address, written, err)
	}()

	// 同步等待两个goroutine都完成
	wg.Wait()
	fmt.Printf("IoBridge: Both directions completed for %s", address)
	return 0
}

// copyWithStats 带流量统计的数据复制函数
func (c *Http2Client) copyWithStats(dst io.Writer, src io.Reader, isUpload bool) (written int64, err error) {
	buffer := make([]byte, 32*1024) // 32KB 缓冲区

	for {
		nr, er := src.Read(buffer)
		if nr > 0 {
			nw, ew := dst.Write(buffer[0:nr])
			if nw > 0 {
				written += int64(nw)
				// 实时更新流量统计
				if isUpload {
					atomic.AddInt64(&c.uploadBytes, int64(nw))
				} else {
					atomic.AddInt64(&c.downloadBytes, int64(nw))
				}
			}
			if ew != nil {
				err = ew
				break
			}
			if nr != nw {
				err = io.ErrShortWrite
				break
			}
		}
		if er != nil {
			if er != io.EOF {
				err = er
			}
			break
		}
	}
	return written, err
}

// 确保Http2Client实现了Client接口
var _ client.Client = (*Http2Client)(nil)
