package quic

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"sync"
	"time"

	"socks5-quic-server/internal/protocol"
	"socks5-quic-server/internal/protocol/pb"
	"socks5-quic-server/pkg/logger"

	"github.com/quic-go/quic-go"
	"google.golang.org/protobuf/proto"
)

// ConnectionPool QUIC连接池
type ConnectionPool struct {
	serverAddr    string
	token         string
	clientID      string
	skipTLSVerify bool
	debug         bool

	conn          quic.Connection
	authenticated bool
	mu            sync.RWMutex

	// 连接状态
	connectMu sync.Mutex
}

// NewConnectionPool 创建连接池
func NewConnectionPool(serverAddr, token, clientID string, skipTLSVerify, debug bool) *ConnectionPool {
	return &ConnectionPool{
		serverAddr:    serverAddr,
		token:         token,
		clientID:      clientID,
		skipTLSVerify: skipTLSVerify,
		debug:         debug,
	}
}

// GetConnection 获取已认证的QUIC连接 - 修复死锁风险
func (cp *ConnectionPool) GetConnection() (quic.Connection, error) {
	// 首先快速检查现有连接（只读检查）
	cp.mu.RLock()
	if cp.conn != nil && cp.authenticated && cp.conn.Context().Err() == nil {
		conn := cp.conn
		cp.mu.RUnlock()
		return conn, nil
	}
	cp.mu.RUnlock()

	// 需要建立新连接，使用单一锁保护整个过程
	return cp.ensureConnection()
}

// ensureConnection 确保连接可用 - 简化锁逻辑
func (cp *ConnectionPool) ensureConnection() (quic.Connection, error) {
	// 使用单一的连接锁来保护整个建立连接的过程
	cp.connectMu.Lock()
	defer cp.connectMu.Unlock()

	// 在连接锁保护下再次检查（避免并发重复连接）
	if cp.conn != nil && cp.authenticated && cp.conn.Context().Err() == nil {
		return cp.conn, nil
	}

	if cp.debug {
		logger.Info("quic", "开始建立新的QUIC连接: %s", cp.serverAddr)
	}

	// 建立QUIC连接
	conn, err := cp.connectToServer()
	if err != nil {
		logger.Info("quic", "连接服务器失败 %s", err.Error())
		return nil, fmt.Errorf("连接服务器失败: %v", err)
	}

	// 执行认证
	if err := cp.authenticate(conn); err != nil {
		conn.CloseWithError(0, "auth failed")
		logger.Info("quic", "认证失败 %s", err.Error())
		return nil, fmt.Errorf("认证失败: %v", err)
	}

	// 在连接锁保护下更新连接状态（避免使用额外的锁）
	if cp.conn != nil {
		cp.conn.CloseWithError(0, "replaced")
	}
	cp.conn = conn
	cp.authenticated = true

	if cp.debug {
		logger.Info("quic", "✅ QUIC连接建立成功，Token: %s", maskToken(cp.token))
	}

	// 启动连接监控
	go cp.monitorConnection(conn)

	return conn, nil
}

// connectToServer 连接到服务器（支持0-RTT）
func (cp *ConnectionPool) connectToServer() (quic.Connection, error) {
	host, _, err := net.SplitHostPort(cp.serverAddr)
	if err != nil {
		return nil, fmt.Errorf("invalid server address: %w", err)
	}

	tlsConfig := &tls.Config{
		ServerName:         host,
		InsecureSkipVerify: cp.skipTLSVerify,
		NextProtos:         []string{"socks5-quic"},
		// 启用会话票据以支持0-RTT
		ClientSessionCache: tls.NewLRUClientSessionCache(100),
	}

	quicConfig := &quic.Config{
		MaxIdleTimeout:          30 * time.Second,
		MaxIncomingStreams:      100,
		MaxIncomingUniStreams:   100,
		KeepAlivePeriod:         10 * time.Second,
		Allow0RTT:               false,
		DisablePathMTUDiscovery: false,
	}

	conn, err := quic.DialAddr(context.Background(), cp.serverAddr, tlsConfig, quicConfig)
	if err != nil {
		return nil, err
	}

	// 检查是否使用了0-RTT
	if cp.debug {
		connState := conn.ConnectionState()
		if connState.TLS.DidResume {
			logger.Info("quic", "�� 使用了会话恢复（可能包含0-RTT）")
		}
		if connState.Used0RTT {
			logger.Info("quic", "⚡ 成功使用0-RTT连接！")
		}
	}

	return conn, nil
}

// authenticate 执行认证
func (cp *ConnectionPool) authenticate(conn quic.Connection) error {
	// 创建认证流
	stream, err := conn.OpenStreamSync(context.Background())
	if err != nil {
		return fmt.Errorf("创建认证流失败: %v", err)
	}
	defer stream.Close()

	// 构造认证请求
	authReq := &pb.AuthRequest{
		Version:   1,
		Token:     cp.token,
		ClientId:  cp.clientID,
		Timestamp: uint64(time.Now().Unix()),
		Nonce:     uint32(time.Now().UnixNano() % 0xFFFFFFFF),
	}

	// 序列化认证请求
	data, err := proto.Marshal(authReq)
	if err != nil {
		return fmt.Errorf("序列化认证请求失败: %v", err)
	}

	// 发送认证请求
	if err := protocol.WriteMessage(stream, data); err != nil {
		return fmt.Errorf("发送认证请求失败: %v", err)
	}

	// 读取认证响应
	respData, err := protocol.ReadMessage(stream)
	if err != nil {
		return fmt.Errorf("读取认证响应失败: %v", err)
	}

	// 解析认证响应
	var authResp pb.AuthResponse
	if err := proto.Unmarshal(respData, &authResp); err != nil {
		return fmt.Errorf("解析认证响应失败: %v", err)
	}

	// 检查认证结果
	if authResp.Status != pb.AuthStatus_AUTH_SUCCESS {
		return fmt.Errorf("认证失败source: %s", authResp.Message)
	}

	if cp.debug {
		logger.Info("quic", "✅ 认证成功: UserID=%s, ServerID=%s", authResp.UserId, authResp.ServerId)
	}

	return nil
}

// monitorConnection 监控连接状态 - 使用统一的锁机制
func (cp *ConnectionPool) monitorConnection(conn quic.Connection) {
	<-conn.Context().Done()

	// 使用连接锁而不是状态锁，确保与ensureConnection一致
	cp.connectMu.Lock()
	if cp.conn == conn {
		cp.conn = nil
		cp.authenticated = false
		if cp.debug {
			logger.Info("quic", "🔌 QUIC连接已断开")
		}
	}
	cp.connectMu.Unlock()
}

// CreateStream 创建新的Stream用于SOCKS连接
func (cp *ConnectionPool) CreateStream(host string, port uint32) (quic.Stream, error) {
	logger.Info("quic", "🔍 CreateStream - Input: host='%s', port=%d (0x%X)", host, port, port)

	conn, err := cp.GetConnection()
	if err != nil {
		return nil, err
	}

	// 创建新的流
	stream, err := conn.OpenStreamSync(context.Background())
	if err != nil {
		return nil, fmt.Errorf("创建流失败: %v", err)
	}

	// 构造连接请求
	connectReq := &pb.ConnectRequest{
		AddrType: pb.AddressType_DOMAIN,
		Address:  host,
		Port:     port,
	}

	// 检查是否为IP地址
	if ip := net.ParseIP(host); ip != nil {
		if ip.To4() != nil {
			connectReq.AddrType = pb.AddressType_IPV4
		} else {
			connectReq.AddrType = pb.AddressType_IPV6
		}
	}

	logger.Info("quic", "🔍 CreateStream - ConnectRequest before marshal: Address='%s', Port=%d (0x%X), AddrType=%v",
		connectReq.Address, connectReq.Port, connectReq.Port, connectReq.AddrType)

	// 序列化连接请求
	data, err := proto.Marshal(connectReq)
	if err != nil {
		stream.Close()
		return nil, fmt.Errorf("序列化连接请求失败: %v", err)
	}

	logger.Info("quic", "🔍 CreateStream - Marshaled data length: %d bytes", len(data))

	// 发送连接请求
	if err := protocol.WriteMessage(stream, data); err != nil {
		stream.Close()
		return nil, fmt.Errorf("发送连接请求失败: %v", err)
	}

	logger.Info("quic", "🔍 CreateStream - Data sent to server")

	// 读取连接响应
	respData, err := protocol.ReadMessage(stream)
	if err != nil {
		stream.Close()
		return nil, fmt.Errorf("读取连接响应失败: %v", err)
	}

	// 解析连接响应
	var connectResp pb.ConnectResponse
	if err := proto.Unmarshal(respData, &connectResp); err != nil {
		stream.Close()
		return nil, fmt.Errorf("解析连接响应失败: %v", err)
	}

	// 检查连接结果
	if !connectResp.Success {
		stream.Close()
		return nil, fmt.Errorf("连接失败: %s", connectResp.Error)
	}

	if cp.debug {
		logger.Info("quic", "✅ 成功建立Stream: %s:%d (StreamID: %d)", host, port, stream.StreamID())
	}

	return stream, nil
}

// Close 关闭连接池 - 使用统一的锁机制
func (cp *ConnectionPool) Close() error {
	cp.connectMu.Lock()
	defer cp.connectMu.Unlock()

	if cp.conn != nil {
		err := cp.conn.CloseWithError(0, "client shutdown")
		cp.conn = nil
		cp.authenticated = false
		return err
	}
	return nil
}

// GetStats 获取连接统计 - 保持读锁用于非修改操作
func (cp *ConnectionPool) GetStats() map[string]interface{} {
	cp.mu.RLock()
	defer cp.mu.RUnlock()

	stats := map[string]interface{}{
		"server_addr":   cp.serverAddr,
		"client_id":     cp.clientID,
		"authenticated": cp.authenticated,
		"connected":     cp.conn != nil,
	}

	if cp.conn != nil {
		connStats := cp.conn.ConnectionState()
		stats["tls_version"] = connStats.TLS.Version
		stats["cipher_suite"] = connStats.TLS.CipherSuite
		stats["negotiated_protocol"] = connStats.TLS.NegotiatedProtocol
	}

	return stats
}

// maskToken 掩码Token用于日志显示
func maskToken(token string) string {
	if len(token) == 0 {
		return "***"
	}
	if len(token) <= 8 {
		return "***"
	}
	return token[:4] + "***" + token[len(token)-4:]
}
