package server

import (
	"fmt"
	"log"
	"net"
	"sync/atomic"
	"time"
)

// ExampleHTTP2Connection HTTP2连接的示例实现
// 这是一个简化版本，展示如何实现ConnectionHandler接口
type ExampleHTTP2Connection struct {
	*BaseConnection

	// HTTP2特定字段
	maxFrameSize int
	maxStreams   int
}

// NewExampleHTTP2Connection 创建HTTP2连接示例
func NewExampleHTTP2Connection(clientAddr, localAddr net.Addr, maxStreams int) *ExampleHTTP2Connection {
	base := NewBaseConnection(clientAddr, localAddr, ConnectionTypeHTTP2, nil)

	return &ExampleHTTP2Connection{
		BaseConnection: base,
		maxFrameSize:   16 * 1024, // 16KB
		maxStreams:     maxStreams,
	}
}

// Start 启动HTTP2连接处理
func (c *ExampleHTTP2Connection) Start() error {
	c.SetStatus("active")

	log.Printf("🚀 HTTP2连接启动: %s", c.clientAddr)

	// 这里应该启动HTTP2帧处理逻辑
	// 为了示例，我们只是模拟一些流量
	go c.simulateTraffic()

	return nil
}

// Stop 停止HTTP2连接处理
func (c *ExampleHTTP2Connection) Stop() error {
	c.SetStatus("stopping")

	log.Printf("🛑 HTTP2连接停止: %s", c.clientAddr)

	return c.Close()
}

// GetStats 获取HTTP2连接统计（重写以添加协议特定信息）
func (c *ExampleHTTP2Connection) GetStats() ConnectionStats {
	stats := c.BaseConnection.GetStats()

	// 添加HTTP2特定统计
	if stats.Extra == nil {
		stats.Extra = make(map[string]interface{})
	}
	stats.Extra["max_frame_size"] = c.maxFrameSize
	stats.Extra["max_streams"] = c.maxStreams

	return stats
}

// GetConnectionInfo 获取HTTP2连接信息（重写以添加协议特定信息）
func (c *ExampleHTTP2Connection) GetConnectionInfo() ConnectionInfo {
	info := c.BaseConnection.GetConnectionInfo()

	// 添加HTTP2特定信息
	info.MaxStreams = c.maxStreams
	info.IdleTimeout = 30 * time.Second // 示例值

	return info
}

// simulateTraffic 模拟流量（仅用于演示）
func (c *ExampleHTTP2Connection) simulateTraffic() {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for i := 0; i < 10; i++ {
		select {
		case <-c.Context().Done():
			return
		case <-ticker.C:
			// 模拟新流
			c.AddStream()
			c.AddTraffic(1024, 2048) // 模拟1KB上传，2KB下载

			// 模拟流结束
			time.AfterFunc(2*time.Second, func() {
				c.RemoveStream()
			})
		}
	}
}

// ExampleQUICConnection QUIC连接的示例实现
type ExampleQUICConnection struct {
	*BaseConnection

	// QUIC特定字段
	authenticated bool
	sessionID     string
}

// NewExampleQUICConnection 创建QUIC连接示例
func NewExampleQUICConnection(clientAddr, localAddr net.Addr, sessionID string) *ExampleQUICConnection {
	base := NewBaseConnection(clientAddr, localAddr, ConnectionTypeQUIC, nil)

	return &ExampleQUICConnection{
		BaseConnection: base,
		sessionID:      sessionID,
		authenticated:  false,
	}
}

// Start 启动QUIC连接处理
func (c *ExampleQUICConnection) Start() error {
	c.SetStatus("authenticating")

	log.Printf("🚀 QUIC连接启动: %s", c.clientAddr)

	// 模拟认证过程
	go c.simulateAuthentication()

	return nil
}

// Stop 停止QUIC连接处理
func (c *ExampleQUICConnection) Stop() error {
	c.SetStatus("stopping")

	log.Printf("🛑 QUIC连接停止: %s", c.clientAddr)

	return c.Close()
}

// GetStats 获取QUIC连接统计（重写以添加协议特定信息）
func (c *ExampleQUICConnection) GetStats() ConnectionStats {
	stats := c.BaseConnection.GetStats()

	// 添加QUIC特定统计
	if stats.Extra == nil {
		stats.Extra = make(map[string]interface{})
	}
	stats.Extra["session_id"] = c.sessionID
	stats.Extra["authenticated"] = c.authenticated

	return stats
}

// simulateAuthentication 模拟认证过程
func (c *ExampleQUICConnection) simulateAuthentication() {
	// 模拟认证延迟
	time.Sleep(100 * time.Millisecond)

	c.authenticated = true
	c.SetAuthInfo("user123", "client456")
	c.SetStatus("active")

	log.Printf("✅ QUIC连接认证成功: %s -> %s", c.clientAddr, c.userID)

	// 开始处理流量
	c.simulateStreams()
}

// simulateStreams 模拟QUIC流处理
func (c *ExampleQUICConnection) simulateStreams() {
	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()

	for i := 0; i < 20; i++ {
		select {
		case <-c.Context().Done():
			return
		case <-ticker.C:
			// 模拟新流
			c.AddStream()
			c.AddTraffic(512, 1024) // 模拟512B上传，1KB下载

			// 模拟流结束
			time.AfterFunc(1*time.Second, func() {
				c.RemoveStream()
			})
		}
	}
}

// 使用示例函数
func ExampleUsage() {
	// 创建连接管理器
	manager := NewConnectionManager(100, 30*time.Second)
	defer manager.Stop()

	// 模拟客户端地址
	clientAddr, _ := net.ResolveTCPAddr("tcp", "192.168.1.100:12345")
	localAddr, _ := net.ResolveTCPAddr("tcp", "0.0.0.0:8443")

	// 创建HTTP2连接
	http2Conn := NewExampleHTTP2Connection(clientAddr, localAddr, 20)
	manager.AddConnection(http2Conn)
	http2Conn.Start()

	// 创建QUIC连接
	quicConn := NewExampleQUICConnection(clientAddr, localAddr, "session-123")
	manager.AddConnection(quicConn)
	quicConn.Start()

	// 等待一段时间观察统计
	time.Sleep(5 * time.Second)

	// 打印统计信息
	PrintConnectionStats(manager)

	// 清理连接
	http2Conn.Stop()
	quicConn.Stop()
}

// PrintConnectionStats 打印连接统计信息
func PrintConnectionStats(manager *ConnectionManager) {
	stats := manager.GetStats()

	fmt.Printf("\n📊 === 连接管理器统计 ===\n")
	fmt.Printf("总连接数: %d\n", stats.TotalConnections)
	fmt.Printf("活跃连接数: %d\n", stats.ActiveConnections)
	fmt.Printf("最大连接数: %d\n", stats.MaxConnections)

	fmt.Printf("\n按类型统计:\n")
	for connType, count := range stats.ConnectionsByType {
		fmt.Printf("  %s: %d\n", connType, count)
	}

	fmt.Printf("\n连接详情:\n")
	for i, connStats := range stats.ConnectionDetails {
		fmt.Printf("  连接 %d:\n", i+1)
		fmt.Printf("    类型: %s\n", connStats.ProtocolType)
		fmt.Printf("    地址: %s\n", connStats.ClientAddr)
		fmt.Printf("    运行时间: %v\n", connStats.Duration)
		fmt.Printf("    总流数: %d\n", connStats.TotalStreams)
		fmt.Printf("    活跃流数: %d\n", connStats.ActiveStreams)
		fmt.Printf("    上传: %d bytes\n", connStats.BytesUp)
		fmt.Printf("    下载: %d bytes\n", connStats.BytesDown)

		if len(connStats.Extra) > 0 {
			fmt.Printf("    额外信息: %+v\n", connStats.Extra)
		}
	}
	fmt.Printf("========================\n\n")
}

// 原子操作示例（用于并发安全的统计更新）
func AtomicStatsExample() {
	var totalConnections int64
	var totalTraffic int64

	// 模拟多个goroutine同时更新统计
	for i := 0; i < 10; i++ {
		go func(id int) {
			for j := 0; j < 100; j++ {
				atomic.AddInt64(&totalConnections, 1)
				atomic.AddInt64(&totalTraffic, int64(id*j))
				time.Sleep(time.Millisecond)
			}
		}(i)
	}

	time.Sleep(2 * time.Second)

	fmt.Printf("原子操作结果:\n")
	fmt.Printf("总连接数: %d\n", atomic.LoadInt64(&totalConnections))
	fmt.Printf("总流量: %d\n", atomic.LoadInt64(&totalTraffic))
}
