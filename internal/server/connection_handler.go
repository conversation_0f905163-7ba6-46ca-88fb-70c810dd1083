package server

import (
	"context"
	"crypto/rand"
	"fmt"
	"io"
	"net"
	"sync"
	"time"
)

// ConnectionHandler 连接处理器接口
// 统一处理HTTP2和QUIC连接的生命周期管理
type ConnectionHandler interface {
	// 获取连接唯一ID
	GetConnID() string

	// 启动连接处理
	Start() error

	// 停止连接处理
	Stop() error

	// 关闭连接
	io.Closer

	// 获取连接统计信息
	GetStats() ConnectionStats

	// 获取连接基本信息
	GetConnectionInfo() ConnectionInfo

	// 检查连接是否已关闭
	IsClosed() bool

	// 认证相关方法 - 改为基于token
	IsAuthenticated() bool
	GetAuthToken() string
	SetAuthToken(token string)

	// 辅助方法 - 简化嵌套调用
	GetAuthStateFromGlobal() *AuthState
}

// ConnectionStats 连接统计信息
type ConnectionStats struct {
	// 基础信息
	StartTime  time.Time     `json:"start_time"`
	Duration   time.Duration `json:"duration"`
	ClientAddr string        `json:"client_addr"`

	// 流统计
	TotalStreams  int64 `json:"total_streams"`
	ActiveStreams int64 `json:"active_streams"`

	// 流量统计
	BytesUp   int64 `json:"bytes_up"`
	BytesDown int64 `json:"bytes_down"`

	// 协议特定统计
	ProtocolType string                 `json:"protocol_type"` // "HTTP2" 或 "QUIC"
	Extra        map[string]interface{} `json:"extra,omitempty"`
}

// ConnectionInfo 连接基本信息
type ConnectionInfo struct {
	// 网络信息
	ClientAddr string `json:"client_addr"`
	LocalAddr  string `json:"local_addr"`

	// 协议信息
	ProtocolType string `json:"protocol_type"`
	TLSVersion   string `json:"tls_version,omitempty"`

	// 状态信息
	Status    string    `json:"status"` // "connecting", "authenticated", "active", "closing", "closed"
	StartTime time.Time `json:"start_time"`

	// 认证信息（如果适用）
	UserID   string `json:"user_id,omitempty"`
	ClientID string `json:"client_id,omitempty"`

	// 配置信息
	MaxStreams  int           `json:"max_streams,omitempty"`
	IdleTimeout time.Duration `json:"idle_timeout,omitempty"`
}

// ConnectionType 连接类型枚举
type ConnectionType int

const (
	ConnectionTypeHTTP2 ConnectionType = iota + 1
	ConnectionTypeQUIC
)

func (ct ConnectionType) String() string {
	switch ct {
	case ConnectionTypeHTTP2:
		return "HTTP2"
	case ConnectionTypeQUIC:
		return "QUIC"
	default:
		return "Unknown"
	}
}

// BaseConnection 基础连接结构
// 包含所有连接类型的通用字段
type BaseConnection struct {
	// 连接唯一ID
	connID string

	// 网络连接
	clientAddr net.Addr
	localAddr  net.Addr

	// 协议类型
	connType ConnectionType

	// 生命周期控制
	ctx    context.Context
	cancel context.CancelFunc

	// 状态管理
	status    string
	startTime time.Time
	closed    bool

	// 统计信息
	totalStreams  int64
	activeStreams int64
	bytesUp       int64
	bytesDown     int64

	// 认证信息（可选）
	userID   string
	clientID string

	// 认证token（不再直接引用AuthState）
	authToken string
	authMutex sync.RWMutex
}

// generateUUID 生成简单的UUID
func generateUUID() string {
	b := make([]byte, 16)
	rand.Read(b)
	return fmt.Sprintf("%x-%x-%x-%x-%x", b[0:4], b[4:6], b[6:8], b[8:10], b[10:16])
}

// NewBaseConnection 创建基础连接
func NewBaseConnection(clientAddr, localAddr net.Addr, connType ConnectionType, parentCtx context.Context) *BaseConnection {
	// 如果没有提供父context，使用Background
	if parentCtx == nil {
		parentCtx = context.Background()
	}

	ctx, cancel := context.WithCancel(parentCtx)

	return &BaseConnection{
		connID:     generateUUID(),
		clientAddr: clientAddr,
		localAddr:  localAddr,
		connType:   connType,
		ctx:        ctx,
		cancel:     cancel,
		status:     "connecting",
		startTime:  time.Now(),
		closed:     false,
	}
}

// GetConnID 获取连接唯一ID
func (bc *BaseConnection) GetConnID() string {
	return bc.connID
}

// GetConnectionInfo 获取连接基本信息
func (bc *BaseConnection) GetConnectionInfo() ConnectionInfo {
	return ConnectionInfo{
		ClientAddr:   bc.clientAddr.String(),
		LocalAddr:    bc.localAddr.String(),
		ProtocolType: bc.connType.String(),
		Status:       bc.status,
		StartTime:    bc.startTime,
		UserID:       bc.userID,
		ClientID:     bc.clientID,
	}
}

// GetStats 获取基础统计信息
func (bc *BaseConnection) GetStats() ConnectionStats {
	duration := time.Since(bc.startTime)
	if bc.closed {
		// 如果已关闭，计算实际持续时间需要额外处理
		// 这里简化处理，实际实现中可能需要记录关闭时间
	}

	return ConnectionStats{
		StartTime:     bc.startTime,
		Duration:      duration,
		ClientAddr:    bc.clientAddr.String(),
		TotalStreams:  bc.totalStreams,
		ActiveStreams: bc.activeStreams,
		BytesUp:       bc.bytesUp,
		BytesDown:     bc.bytesDown,
		ProtocolType:  bc.connType.String(),
	}
}

// IsClosed 检查连接是否已关闭
func (bc *BaseConnection) IsClosed() bool {
	return bc.closed
}

// SetStatus 设置连接状态
func (bc *BaseConnection) SetStatus(status string) {
	bc.status = status
}

// SetAuthInfo 设置认证信息
func (bc *BaseConnection) SetAuthInfo(userID, clientID string) {
	bc.userID = userID
	bc.clientID = clientID
	bc.status = "authenticated"
}

// SetAuthToken 设置认证token
func (bc *BaseConnection) SetAuthToken(token string) {
	bc.authMutex.Lock()
	defer bc.authMutex.Unlock()
	bc.authToken = token
	if token != "" {
		bc.status = "authenticated"
		// 通过全局AuthManager获取用户信息
		if server := GetGlobalServer(); server != nil {
			if authManager := server.GetAuthManager(); authManager != nil {
				if authState := authManager.GetAuthState(token); authState != nil {
					bc.userID = authState.UserID
					bc.clientID = authState.ClientID
				}
			}
		}
	}
}

// GetAuthToken 获取认证token
func (bc *BaseConnection) GetAuthToken() string {
	bc.authMutex.RLock()
	defer bc.authMutex.RUnlock()
	return bc.authToken
}

// IsAuthenticated 检查是否已认证
func (bc *BaseConnection) IsAuthenticated() bool {
	bc.authMutex.RLock()
	defer bc.authMutex.RUnlock()
	return bc.authToken != ""
}

// GetAuthStateFromGlobal 通过全局AuthManager获取AuthState（辅助方法，简化嵌套）
func (bc *BaseConnection) GetAuthStateFromGlobal() *AuthState {
	token := bc.GetAuthToken()
	if token == "" {
		return nil
	}

	server := GetGlobalServer()
	if server == nil {
		return nil
	}

	authManager := server.GetAuthManager()
	if authManager == nil {
		return nil
	}

	return authManager.GetAuthState(token)
}

// AddStream 增加流计数
func (bc *BaseConnection) AddStream() {
	bc.totalStreams++
	bc.activeStreams++
}

// RemoveStream 减少流计数
func (bc *BaseConnection) RemoveStream() {
	if bc.activeStreams > 0 {
		bc.activeStreams--
	}
}

// AddTraffic 添加流量统计
func (bc *BaseConnection) AddTraffic(bytesUp, bytesDown int64) {
	bc.bytesUp += bytesUp
	bc.bytesDown += bytesDown
}

// Close 关闭基础连接
func (bc *BaseConnection) Close() error {
	bc.cancel()
	bc.closed = true
	bc.status = "closed"
	return nil
}

// Context 获取上下文
func (bc *BaseConnection) Context() context.Context {
	return bc.ctx
}
