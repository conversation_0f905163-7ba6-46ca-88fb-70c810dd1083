package server

import (
	"sync"
	"time"
)

// ConnectionManager 连接管理器
// 统一管理HTTP2和QUIC连接
type ConnectionManager struct {
	mu          sync.RWMutex
	connections map[string]ConnectionHandler // key: connectionID

	// 统计信息
	totalConnections  int64
	activeConnections int64

	// 配置
	maxConnections  int
	cleanupInterval time.Duration

	// 清理任务
	cleanupTicker *time.Ticker
	stopCleanup   chan struct{}
}

// NewConnectionManager 创建连接管理器
func NewConnectionManager(maxConnections int, cleanupInterval time.Duration) *ConnectionManager {
	cm := &ConnectionManager{
		connections:     make(map[string]ConnectionHandler),
		maxConnections:  maxConnections,
		cleanupInterval: cleanupInterval,
		stopCleanup:     make(chan struct{}),
	}

	// 启动清理任务
	if cleanupInterval > 0 {
		cm.startCleanupTask()
	}

	return cm
}

// AddConnection 添加连接
func (cm *ConnectionManager) AddConnection(handler ConnectionHandler) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	// 检查连接数限制
	if cm.maxConnections > 0 && len(cm.connections) >= cm.maxConnections {
		return ErrMaxConnectionsReached
	}

	connectionID := handler.GetConnID()
	cm.connections[connectionID] = handler
	cm.totalConnections++
	cm.activeConnections++

	return nil
}

// RemoveConnection 移除连接
func (cm *ConnectionManager) RemoveConnection(connectionID string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	if handler, exists := cm.connections[connectionID]; exists {
		// 确保连接已关闭
		if !handler.IsClosed() {
			handler.Close()
		}

		delete(cm.connections, connectionID)
		cm.activeConnections--
	}
}

// GetConnection 获取连接
func (cm *ConnectionManager) GetConnection(connectionID string) (ConnectionHandler, bool) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	handler, exists := cm.connections[connectionID]
	return handler, exists
}

// GetAllConnections 获取所有连接
func (cm *ConnectionManager) GetAllConnections() map[string]ConnectionHandler {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	// 返回副本以避免并发问题
	connections := make(map[string]ConnectionHandler)
	for id, handler := range cm.connections {
		connections[id] = handler
	}

	return connections
}

// GetConnectionCount 获取连接数量
func (cm *ConnectionManager) GetConnectionCount() (total, active int64) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	return cm.totalConnections, cm.activeConnections
}

// GetConnectionsByType 按类型获取连接
func (cm *ConnectionManager) GetConnectionsByType(connType ConnectionType) []ConnectionHandler {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	var connections []ConnectionHandler
	for _, handler := range cm.connections {
		info := handler.GetConnectionInfo()
		if info.ProtocolType == connType.String() {
			connections = append(connections, handler)
		}
	}

	return connections
}

// GetStats 获取管理器统计信息
func (cm *ConnectionManager) GetStats() ConnectionManagerStats {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	stats := ConnectionManagerStats{
		TotalConnections:  cm.totalConnections,
		ActiveConnections: cm.activeConnections,
		MaxConnections:    int64(cm.maxConnections),
		ConnectionsByType: make(map[string]int),
		ConnectionDetails: make([]ConnectionStats, 0, len(cm.connections)),
	}

	// 统计各类型连接数量和详细信息
	for _, handler := range cm.connections {
		connStats := handler.GetStats()
		stats.ConnectionDetails = append(stats.ConnectionDetails, connStats)

		// 按类型统计
		stats.ConnectionsByType[connStats.ProtocolType]++
	}

	return stats
}

// CloseAllConnections 关闭所有连接
func (cm *ConnectionManager) CloseAllConnections() {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	for id, handler := range cm.connections {
		handler.Close()
		delete(cm.connections, id)
	}

	cm.activeConnections = 0
}

// Stop 停止连接管理器
func (cm *ConnectionManager) Stop() {
	// 停止清理任务
	if cm.cleanupTicker != nil {
		cm.cleanupTicker.Stop()
		close(cm.stopCleanup)
	}

	// 关闭所有连接
	cm.CloseAllConnections()
}

// startCleanupTask 启动清理任务
func (cm *ConnectionManager) startCleanupTask() {
	cm.cleanupTicker = time.NewTicker(cm.cleanupInterval)

	go func() {
		for {
			select {
			case <-cm.cleanupTicker.C:
				cm.cleanupClosedConnections()
			case <-cm.stopCleanup:
				return
			}
		}
	}()
}

// cleanupClosedConnections 清理已关闭的连接
func (cm *ConnectionManager) cleanupClosedConnections() {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	var toRemove []string

	// 找出已关闭的连接
	for id, handler := range cm.connections {
		if handler.IsClosed() {
			toRemove = append(toRemove, id)
		}
	}

	// 移除已关闭的连接
	for _, id := range toRemove {
		delete(cm.connections, id)
		cm.activeConnections--
	}

	if len(toRemove) > 0 {
		// 可以在这里记录日志
	}
}

// ConnectionManagerStats 连接管理器统计信息
type ConnectionManagerStats struct {
	TotalConnections  int64 `json:"total_connections"`
	ActiveConnections int64 `json:"active_connections"`
	MaxConnections    int64 `json:"max_connections"`

	// 按类型统计
	ConnectionsByType map[string]int `json:"connections_by_type"`

	// 详细连接信息
	ConnectionDetails []ConnectionStats `json:"connection_details"`
}

// 错误定义
var (
	ErrMaxConnectionsReached = NewServerError("max connections reached")
	ErrConnectionNotFound    = NewServerError("connection not found")
	ErrConnectionClosed      = NewServerError("connection closed")
)

// ServerError 服务器错误类型
type ServerError struct {
	Message string
}

func (e *ServerError) Error() string {
	return e.Message
}

func NewServerError(message string) *ServerError {
	return &ServerError{Message: message}
}
