package server

import (
	"bytes"
	"net"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"golang.org/x/net/http2"
)

// 简单的Mock连接，用于测试framer通讯
type TestConn struct {
	readBuf  *bytes.Buffer
	writeBuf *bytes.Buffer
	mu       sync.Mutex
}

func NewTestConn() *TestConn {
	return &TestConn{
		readBuf:  bytes.NewBuffer(nil),
		writeBuf: bytes.NewBuffer(nil),
	}
}

func (c *TestConn) Read(b []byte) (n int, err error) {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.readBuf.Read(b)
}

func (c *TestConn) Write(b []byte) (n int, err error) {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.writeBuf.Write(b)
}

func (c *TestConn) Close() error                       { return nil }
func (c *TestConn) LocalAddr() net.Addr                { return nil }
func (c *TestConn) RemoteAddr() net.Addr               { return nil }
func (c *TestConn) SetDeadline(t time.Time) error      { return nil }
func (c *TestConn) SetReadDeadline(t time.Time) error  { return nil }
func (c *TestConn) SetWriteDeadline(t time.Time) error { return nil }

// 添加测试数据
func (c *TestConn) AddReadData(data []byte) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.readBuf.Write(data)
}

// 获取写入的数据
func (c *TestConn) GetWrittenData() []byte {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.writeBuf.Bytes()
}

// 简单的Mock服务器
func createSimpleTestServer() *HTTP2Server {
	return &HTTP2Server{
		config: &HTTP2Config{
			MaxStreams:  10,
			IdleTimeout: 30 * time.Second,
		},
	}
}

// 测试HTTP2Connection基本创建
func TestHTTP2Connection_Creation(t *testing.T) {
	server := createSimpleTestServer()
	conn := NewTestConn()

	h2conn := NewHTTP2Connection(server, conn)

	if h2conn == nil {
		t.Fatal("HTTP2Connection创建失败")
	}

	if h2conn.framer == nil {
		t.Error("framer未初始化")
	}

	if h2conn.streamManager == nil {
		t.Error("streamManager未初始化")
	}

	if h2conn.peerMaxFrameSize != 16*1024 {
		t.Errorf("默认帧大小错误，期望16384，实际%d", h2conn.peerMaxFrameSize)
	}
}

// 测试客户端前言验证
func TestHTTP2Connection_ClientPreface(t *testing.T) {
	server := createSimpleTestServer()
	conn := NewTestConn()
	h2conn := NewHTTP2Connection(server, conn)

	// 正确的前言
	conn.AddReadData([]byte(http2.ClientPreface))
	err := h2conn.validateClientPreface()
	if err != nil {
		t.Errorf("正确前言验证失败: %v", err)
	}

	// 错误的前言
	conn2 := NewTestConn()
	h2conn2 := NewHTTP2Connection(server, conn2)
	conn2.AddReadData([]byte("wrong preface"))
	err = h2conn2.validateClientPreface()
	if err == nil {
		t.Error("错误前言应该验证失败")
	}
}

// 测试framer写入功能
func TestHTTP2Connection_FramerWrite(t *testing.T) {
	server := createSimpleTestServer()
	conn := NewTestConn()
	h2conn := NewHTTP2Connection(server, conn)

	// 测试写入HEADERS帧
	h2conn.writeHeaders(1, 200, "OK", false)

	// 测试写入DATA帧
	testData := []byte("Hello World")
	err := h2conn.writeData(1, false, testData)
	if err != nil {
		t.Errorf("写入DATA帧失败: %v", err)
	}

	// 测试写入PING帧
	pingData := [8]byte{1, 2, 3, 4, 5, 6, 7, 8}
	h2conn.writePing(false, pingData)

	// 验证帧计数
	if atomic.LoadInt64(&h2conn.framesSent) != 3 {
		t.Errorf("发送帧数错误，期望3，实际%d", atomic.LoadInt64(&h2conn.framesSent))
	}

	// 验证有数据写入
	written := conn.GetWrittenData()
	if len(written) == 0 {
		t.Error("没有数据写入连接")
	}
}

// 测试PING帧处理
func TestHTTP2Connection_PingFrameHandling(t *testing.T) {
	server := createSimpleTestServer()
	conn := NewTestConn()
	h2conn := NewHTTP2Connection(server, conn)

	// 创建PING帧
	var buf bytes.Buffer
	framer := http2.NewFramer(&buf, &buf)
	pingData := [8]byte{1, 2, 3, 4, 5, 6, 7, 8}
	framer.WritePing(false, pingData)

	// 模拟读取PING帧
	conn.AddReadData(buf.Bytes())
	framer2 := http2.NewFramer(conn, conn)

	frame, err := framer2.ReadFrame()
	if err != nil {
		t.Fatalf("读取PING帧失败: %v", err)
	}

	pingFrame, ok := frame.(*http2.PingFrame)
	if !ok {
		t.Fatal("帧类型不是PING")
	}

	// 处理PING帧
	h2conn.handlePingFrame(pingFrame)

	// 验证发送了PING ACK
	if atomic.LoadInt64(&h2conn.framesSent) != 1 {
		t.Errorf("应该发送1个PING ACK帧，实际发送了%d个", atomic.LoadInt64(&h2conn.framesSent))
	}
}

// 测试并发写入
func TestHTTP2Connection_ConcurrentWrite(t *testing.T) {
	server := createSimpleTestServer()
	conn := NewTestConn()
	h2conn := NewHTTP2Connection(server, conn)

	const numGoroutines = 5
	const numWrites = 20

	var wg sync.WaitGroup
	wg.Add(numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer wg.Done()
			for j := 0; j < numWrites; j++ {
				streamID := uint32(id*numWrites + j + 1)
				h2conn.writeData(streamID, false, []byte("test"))
			}
		}(i)
	}

	wg.Wait()

	expectedFrames := int64(numGoroutines * numWrites)
	actualFrames := atomic.LoadInt64(&h2conn.framesSent)

	if actualFrames != expectedFrames {
		t.Errorf("并发写入帧数错误，期望%d，实际%d", expectedFrames, actualFrames)
	}
}

// 测试连接状态
func TestHTTP2Connection_ConnectionState(t *testing.T) {
	server := createSimpleTestServer()
	conn := NewTestConn()
	h2conn := NewHTTP2Connection(server, conn)

	// 初始状态
	if h2conn.IsClosed() {
		t.Error("新连接不应该是关闭状态")
	}

	// 关闭连接
	err := h2conn.Close()
	if err != nil {
		t.Errorf("关闭连接失败: %v", err)
	}

	if !h2conn.IsClosed() {
		t.Error("连接应该是关闭状态")
	}

	// 重复关闭应该安全
	err = h2conn.Close()
	if err != nil {
		t.Errorf("重复关闭失败: %v", err)
	}
}

// 性能测试
func BenchmarkHTTP2Connection_WriteData(b *testing.B) {
	server := createSimpleTestServer()
	conn := NewTestConn()
	h2conn := NewHTTP2Connection(server, conn)

	data := []byte("benchmark test data")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		h2conn.writeData(uint32(i%1000+1), false, data)
	}
}
