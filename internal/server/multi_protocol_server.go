package server

import (
	"context"
	"crypto/tls"
	"fmt"
	"log"
	"sync"
	"sync/atomic"
	"time"
)

// 全局服务器实例
var GlobalServer *MultiProtocolServer

// GetGlobalServer 获取全局服务器实例
func GetGlobalServer() *MultiProtocolServer {
	return GlobalServer
}

// MultiProtocolConfig 多协议服务器配置
type MultiProtocolConfig struct {
	// 通用配置
	MaxConnections  int           // 最大连接数（所有协议共享）
	IdleTimeout     time.Duration // 空闲超时时间
	CleanupInterval time.Duration // 清理间隔
	StatsInterval   time.Duration // 统计输出间隔
	AuthConfig      *AuthConfig   // 认证配置
	EventLogger     EventLogger   // 事件日志器（可选）
	EnableDebug     bool          // 启用调试模式

	// QUIC服务配置
	QUICEnabled    bool        // 是否启用QUIC服务
	QUICListenAddr string      // QUIC监听地址
	QUICTLSConfig  *tls.Config // QUIC TLS配置

	// HTTP2服务配置
	HTTP2Enabled        bool                 // 是否启用HTTP2服务
	HTTP2ListenAddr     string               // HTTP2监听地址
	HTTP2TLSConfig      *tls.Config          // HTTP2 TLS配置
	HTTP2MaxStreams     int                  // HTTP2单连接最大流数
	HTTP2ConnPoolConfig *HTTP2ConnPoolConfig // HTTP2连接池配置
}

// HTTP2ConnPoolConfig HTTP2连接池配置
type HTTP2ConnPoolConfig struct {
	MaxIdle int           // 最大空闲连接数
	IdleTTL time.Duration // 空闲连接TTL
	MaxKeys int           // 最大键数量
}

// MultiProtocolServer 多协议服务器
type MultiProtocolServer struct {
	// 共享组件
	authManager       *AuthManager
	trafficManager    *TrafficManager
	connectionManager *ConnectionManager
	eventLogger       EventLogger
	apiClient         *APIClient

	// 协议服务
	quicServer  *Server
	http2Server *HTTP2Server

	// 控制
	ctx     context.Context
	cancel  context.CancelFunc
	wg      sync.WaitGroup
	config  *MultiProtocolConfig
	closing int32 // 关闭状态标志，使用原子操作

	bytesUp   int64
	bytesDown int64

	// 基本信息
	startTime time.Time
}

// NewMultiProtocolServer 创建多协议服务器
func NewMultiProtocolServer(config *MultiProtocolConfig) (*MultiProtocolServer, error) {
	// 验证配置
	if err := validateMultiProtocolConfig(config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	ctx, cancel := context.WithCancel(context.Background())

	// 设置默认值
	setMultiProtocolDefaults(config)

	// 创建事件日志器
	var eventLogger EventLogger
	if config.EventLogger != nil {
		eventLogger = config.EventLogger
	} else {
		eventLogger = NewDefaultEventLogger(config.EnableDebug)
	}

	// 创建API客户端（全局共享）
	var apiClient *APIClient
	if config.AuthConfig.APIUrl != "" {
		apiClient = NewAPIClient(
			config.AuthConfig.APIUrl,
			config.AuthConfig.APIKey,
			time.Duration(config.AuthConfig.Timeout)*time.Second,
		)
	}

	// 创建认证管理器（共享）
	authManager := NewAuthManager(config.AuthConfig)

	// 创建流量管理器（共享）
	trafficManager := NewTrafficManager(1000)

	// 创建连接管理器（共享）
	connectionManager := NewConnectionManager(config.MaxConnections, config.CleanupInterval)

	// 创建多协议服务器实例
	server := &MultiProtocolServer{
		authManager:       authManager,
		trafficManager:    trafficManager,
		connectionManager: connectionManager,
		eventLogger:       eventLogger,
		apiClient:         apiClient,
		ctx:               ctx,
		cancel:            cancel,
		config:            config,
		startTime:         time.Now(),
	}

	// 创建协议服务
	if err := server.createProtocolServices(); err != nil {
		return nil, fmt.Errorf("创建协议服务失败: %w", err)
	}

	// 设置全局服务器实例
	GlobalServer = server
	trafficManager.RegisterHandler(NewTotalTrafficHandler(server))

	return server, nil
}

// createProtocolServices 创建协议服务
func (s *MultiProtocolServer) createProtocolServices() error {
	// 创建QUIC服务
	if s.config.QUICEnabled {
		quicConfig := &Config{
			ListenAddr:      s.config.QUICListenAddr,
			TLSConfig:       s.config.QUICTLSConfig,
			MaxConnections:  s.config.MaxConnections,
			IdleTimeout:     s.config.IdleTimeout,
			CleanupInterval: s.config.CleanupInterval,
			StatsInterval:   s.config.StatsInterval,
			AuthConfig:      s.config.AuthConfig,
			EnableDebug:     s.config.EnableDebug,
		}

		quicServer, err := NewServer(quicConfig, s.authManager, s.trafficManager, s.connectionManager, s.eventLogger)
		if err != nil {
			return fmt.Errorf("创建QUIC服务失败: %w", err)
		}

		s.quicServer = quicServer
	}

	// 创建HTTP2服务
	if s.config.HTTP2Enabled {
		http2Config := &HTTP2Config{
			ListenAddr:      s.config.HTTP2ListenAddr,
			TLSConfig:       s.config.HTTP2TLSConfig,
			MaxConnections:  s.config.MaxConnections,
			MaxStreams:      s.config.HTTP2MaxStreams,
			IdleTimeout:     s.config.IdleTimeout,
			CleanupInterval: s.config.CleanupInterval,
			StatsInterval:   s.config.StatsInterval,
			EnableDebug:     s.config.EnableDebug,
		}

		// 设置连接池配置
		if s.config.HTTP2ConnPoolConfig != nil {
			http2Config.ConnPoolMaxIdle = s.config.HTTP2ConnPoolConfig.MaxIdle
			http2Config.ConnPoolIdleTTL = s.config.HTTP2ConnPoolConfig.IdleTTL
			http2Config.ConnPoolMaxKeys = s.config.HTTP2ConnPoolConfig.MaxKeys
		}

		http2Server, err := NewHTTP2Server(http2Config, s.authManager, s.trafficManager, s.connectionManager, s.eventLogger)
		if err != nil {
			return fmt.Errorf("创建HTTP2服务失败: %w", err)
		}

		s.http2Server = http2Server
	}

	return nil
}

// Start 启动多协议服务器
func (s *MultiProtocolServer) Start() error {
	log.Printf("🚀 启动多协议SOCKS5服务器...")

	// 启动QUIC服务
	if s.config.QUICEnabled && s.quicServer != nil {
		log.Printf("📡 启动QUIC服务: %s", s.config.QUICListenAddr)
		s.wg.Add(1)
		go func() {
			defer s.wg.Done()
			if err := s.quicServer.Start(); err != nil {
				log.Printf("❌ QUIC服务启动失败: %v", err)
			}
		}()
	}

	// 启动HTTP2服务
	if s.config.HTTP2Enabled && s.http2Server != nil {
		log.Printf("📡 启动HTTP2服务: %s", s.config.HTTP2ListenAddr)
		s.wg.Add(1)
		go func() {
			defer s.wg.Done()
			if err := s.http2Server.Start(); err != nil {
				log.Printf("❌ HTTP2服务启动失败: %v", err)
			}
		}()
	}

	log.Printf("✅ 多协议服务器启动成功")
	s.printStartupInfo()

	// 启动认证清理任务
	s.wg.Add(1)
	go s.authCleanupTask()

	// 启动流量上报任务（每10秒）
	s.wg.Add(1)
	go s.trafficReportTask()

	return nil
}

// authCleanupTask 认证清理任务
func (s *MultiProtocolServer) authCleanupTask() {
	defer s.wg.Done()

	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.authManager.CleanupExpired(s.config.IdleTimeout)
		}
	}
}

// trafficReportTask 流量上报任务（每10秒）
func (s *MultiProtocolServer) trafficReportTask() {
	defer s.wg.Done()

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	log.Printf("📊 启动流量上报任务，每10秒上报一次")

	for {
		select {
		case <-s.ctx.Done():
			//退出时候进行最后一次上报
			s.ReportTotalTraffic()
			log.Printf("⏹️  流量上报任务已停止")
			return
		case <-ticker.C:
			if err := s.ReportTotalTraffic(); err != nil {
				log.Printf("⚠️  流量上报失败: %v", err)
			} else {
				if s.config.EnableDebug {
					log.Printf("✅ 流量上报成功")
				}
			}
		}
	}
}

// Stop 停止多协议服务器
func (s *MultiProtocolServer) Stop() error {
	log.Printf("🛑 正在停止多协议服务器...")

	// 设置关闭状态
	atomic.StoreInt32(&s.closing, 1)

	s.cancel()

	// 首先停止认证管理器，阻止新的认证请求
	if s.authManager != nil {
		log.Printf("⏹️  停止认证管理器...")
		s.authManager.Stop()
	}

	// 停止连接管理器，清理现有连接
	if s.connectionManager != nil {
		log.Printf("⏹️  停止连接管理器...")
		s.connectionManager.Stop()
	}

	// 关闭流量管理器，停止流量统计
	if s.trafficManager != nil {
		log.Printf("⏹️  关闭流量管理器...")
		s.trafficManager.Close()
	}

	// 停止HTTP2服务
	if s.http2Server != nil {
		log.Printf("⏹️  停止HTTP2服务...")
		if err := s.http2Server.Stop(); err != nil {
			log.Printf("❌ 停止HTTP2服务时出错: %v", err)
		}
	}

	// 停止QUIC服务
	if s.quicServer != nil {
		log.Printf("⏹️  停止QUIC服务...")
		if err := s.quicServer.Stop(); err != nil {
			log.Printf("❌ 停止QUIC服务时出错: %v", err)
		}
	}

	// 等待所有服务结束
	done := make(chan struct{})
	go func() {
		s.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		log.Printf("✅ 所有服务已正常结束")
	case <-time.After(15 * time.Second):
		log.Printf("⚠️  等待服务停止超时，强制退出")
	}

	log.Printf("✅ 多协议服务器已停止")
	return nil
}

// printStartupInfo 打印启动信息
func (s *MultiProtocolServer) printStartupInfo() {
	log.Printf("📊 === 多协议服务器配置 ===")
	if s.config.QUICEnabled {
		log.Printf("🔵 QUIC服务: %s", s.config.QUICListenAddr)
	}
	if s.config.HTTP2Enabled {
		log.Printf("🟢 HTTP2服务: %s", s.config.HTTP2ListenAddr)
	}
	log.Printf("⚙️  最大连接数: %d", s.config.MaxConnections)
	log.Printf("⏰ 空闲超时: %v", s.config.IdleTimeout)
	log.Printf("🔐 认证模式: %s", s.config.AuthConfig.Mode)
	log.Printf("========================")
}

// GetStats 获取聚合统计信息
func (s *MultiProtocolServer) GetStats() map[string]interface{} {
	stats := map[string]interface{}{
		"uptime": time.Since(s.startTime).Seconds(),
		"protocols": map[string]bool{
			"quic":  s.config.QUICEnabled,
			"http2": s.config.HTTP2Enabled,
		},
	}

	if s.quicServer != nil {
		stats["quic"] = s.quicServer.GetStats()
	}

	if s.http2Server != nil {
		stats["http2"] = s.http2Server.GetStats()
	}

	// 获取用户统计
	stats["users"] = s.authManager.GetStats()

	// 获取流量统计
	if s.trafficManager != nil {
		stats["traffic_detail"] = s.trafficManager.GetAllStats()
	}

	// 获取连接管理器统计
	if s.connectionManager != nil {
		stats["connections"] = s.connectionManager.GetStats()
	}

	return stats
}

// 配置验证和默认值设置
func validateMultiProtocolConfig(config *MultiProtocolConfig) error {
	if !config.QUICEnabled && !config.HTTP2Enabled {
		return fmt.Errorf("至少需要启用一个协议服务（QUIC或HTTP2）")
	}

	if config.QUICEnabled && config.QUICListenAddr == "" {
		return fmt.Errorf("启用QUIC服务时必须指定监听地址")
	}

	if config.HTTP2Enabled && config.HTTP2ListenAddr == "" {
		return fmt.Errorf("启用HTTP2服务时必须指定监听地址")
	}

	if config.MaxConnections <= 0 {
		return fmt.Errorf("最大连接数必须大于0")
	}

	return nil
}

func setMultiProtocolDefaults(config *MultiProtocolConfig) {
	if config.IdleTimeout == 0 {
		config.IdleTimeout = 5 * time.Minute
	}
	if config.CleanupInterval == 0 {
		config.CleanupInterval = 1 * time.Minute
	}
	if config.StatsInterval == 0 {
		config.StatsInterval = 5 * time.Minute
	}
	if config.HTTP2MaxStreams == 0 {
		config.HTTP2MaxStreams = 20
	}
	// if config.AuthConfig == nil {
	// 	config.AuthConfig = &AuthConfig{
	// 		Mode:      "token",
	// 		Timeout:   10,
	// 		CacheSize: 1000,
	// 		CacheTTL:  300,

	// 	}
	// }
	if config.HTTP2ConnPoolConfig == nil {
		config.HTTP2ConnPoolConfig = &HTTP2ConnPoolConfig{
			MaxIdle: 32,
			IdleTTL: 2 * time.Minute,
			MaxKeys: 10000,
		}
	}
}

// GetAPIClient 获取API客户端
func (s *MultiProtocolServer) GetAPIClient() *APIClient {
	if s == nil {
		return nil
	}
	return s.apiClient
}

// GetAuthManager 获取认证管理器
func (s *MultiProtocolServer) GetAuthManager() *AuthManager {
	if s == nil {
		return nil
	}
	return s.authManager
}

// GetTrafficManager 获取流量管理器
func (s *MultiProtocolServer) GetTrafficManager() *TrafficManager {
	if s == nil {
		return nil
	}
	return s.trafficManager
}

// GetConnectionManager 获取连接管理器
func (s *MultiProtocolServer) GetConnectionManager() *ConnectionManager {
	if s == nil {
		return nil
	}
	return s.connectionManager
}

// GetEventLogger 获取事件日志器
func (s *MultiProtocolServer) GetEventLogger() EventLogger {
	if s == nil {
		return nil
	}
	return s.eventLogger
}

// IsClosing 检查服务器是否正在关闭
func (s *MultiProtocolServer) IsClosing() bool {
	if s == nil {
		return true
	}
	return atomic.LoadInt32(&s.closing) == 1
}

// ReportTotalTraffic 上报总流量
func (s *MultiProtocolServer) ReportTotalTraffic() error {
	apiClient := s.GetAPIClient()
	if apiClient == nil {
		return fmt.Errorf("API client not configured")
	}

	bytesUp := atomic.SwapInt64(&s.bytesUp, 0)
	bytesDown := atomic.SwapInt64(&s.bytesDown, 0)

	data := &ReportTotalTrafficRequest{
		TotalUp:     bytesUp,
		TotalDown:   bytesDown,
		ActiveUsers: s.authManager.GetOnlineTotalUsers(),
		Timestamp:   time.Now().Unix(),
	}

	_, err := apiClient.ReportTotalTraffic(data)
	return err
}
