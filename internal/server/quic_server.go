package server

import (
	"context"
	"crypto/tls"
	"fmt"
	"log"
	"sync"
	"sync/atomic"
	"time"

	"socks5-quic-server/internal/protocol"

	"github.com/quic-go/quic-go"
)

// Server SOCKS5 over QUIC 服务器
type Server struct {
	listener          *quic.Listener
	protocolService   protocol.ProtocolService
	authManager       *AuthManager
	trafficManager    *TrafficManager    // 新增：流量管理器
	connectionManager *ConnectionManager // 新增：连接管理器
	eventLogger       EventLogger        // 新增：事件日志器
	ctx               context.Context
	cancel            context.CancelFunc
	wg                sync.WaitGroup
	config            *Config

	// 统计信息 - 服务器统计（TrafficManager可以直接引用和更新这些字段）
	startTime             time.Time
	totalQUICConnections  int64 // QUIC连接总数
	activeQUICConnections int64 // 活跃QUIC连接数
	rejectedConnections   int64 // 拒绝的连接数
	totalStreams          int64 // Stream总数
	activeStreams         int64 // 活跃Stream数
	totalBytesUploaded    int64 // 总上传字节数
	totalBytesDownloaded  int64 // 总下载字节数
}

// Config 服务器配置
type Config struct {
	ListenAddr      string        // 监听地址
	TLSConfig       *tls.Config   // TLS配置
	MaxConnections  int           // 最大连接数
	IdleTimeout     time.Duration // 空闲超时时间
	CleanupInterval time.Duration // 清理间隔
	StatsInterval   time.Duration // 统计输出间隔
	AuthConfig      *AuthConfig   // 认证配置
	EventLogger     EventLogger   // 事件日志器（可选）
	EnableDebug     bool          // 启用调试模式
}

// NewServer 创建新的服务器实例
func NewServer(
	config *Config,
	authManager *AuthManager,
	trafficManager *TrafficManager,
	connectionManager *ConnectionManager,
	eventLogger EventLogger,
) (*Server, error) {
	// 验证必需参数
	if config == nil {
		return nil, fmt.Errorf("config不能为nil")
	}
	if authManager == nil {
		return nil, fmt.Errorf("authManager不能为nil")
	}
	if trafficManager == nil {
		return nil, fmt.Errorf("trafficManager不能为nil")
	}
	if connectionManager == nil {
		return nil, fmt.Errorf("connectionManager不能为nil")
	}
	if eventLogger == nil {
		return nil, fmt.Errorf("eventLogger不能为nil")
	}

	// 创建协议服务
	protocolService := protocol.NewProtocolService()

	ctx, cancel := context.WithCancel(context.Background())

	// 设置默认统计间隔
	if config.StatsInterval == 0 {
		config.StatsInterval = 5 * time.Minute
	}

	// 创建完整的服务器实例
	server := &Server{
		protocolService:   protocolService,
		authManager:       authManager,
		trafficManager:    trafficManager,
		connectionManager: connectionManager,
		eventLogger:       eventLogger,
		ctx:               ctx,
		cancel:            cancel,
		config:            config,
		startTime:         time.Now(),
	}

	// 注册流量处理器
	server.registerTrafficHandlers()

	return server, nil
}

// registerTrafficHandlers 注册流量处理器
func (s *Server) registerTrafficHandlers() {
	// 注册用户级流量处理器
	userHandler := NewUserTrafficHandler(s.authManager)
	s.trafficManager.RegisterHandler(userHandler)

	// 注册QUIC服务器级流量处理器
	serverHandler := NewServerTrafficHandler(s)
	s.trafficManager.RegisterHandler(serverHandler)
}

// Start 启动服务器
func (s *Server) Start() error {
	// 创建支持0-RTT的QUIC配置
	quicConfig := &quic.Config{
		MaxIdleTimeout:        s.config.IdleTimeout,
		MaxIncomingStreams:    1000,
		MaxIncomingUniStreams: 100,
		KeepAlivePeriod:       30 * time.Second,
		// 启用0-RTT支持
		Allow0RTT: true,
	}

	// 创建QUIC监听器
	listener, err := quic.ListenAddr(s.config.ListenAddr, s.config.TLSConfig, quicConfig)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %w", s.config.ListenAddr, err)
	}

	s.listener = listener

	// 触发服务器启动事件
	s.eventLogger.ServerStart(s.config.ListenAddr, "QUIC")

	log.Printf("🚀 SOCKS5 over QUIC 服务器启动成功")
	log.Printf("📡 监听地址: %s", s.config.ListenAddr)
	log.Printf("⚙️  最大连接数: %d", s.config.MaxConnections)
	log.Printf("⏰ 空闲超时: %v", s.config.IdleTimeout)

	// // 启动清理任务
	// s.wg.Add(1)
	// go s.cleanupTask()

	// 启动统计任务
	s.wg.Add(1)
	go s.statsTask()

	// 启动认证清理任务
	// s.wg.Add(1)
	// go s.authCleanupTask()

	// 接受连接
	s.wg.Add(1)
	go s.acceptConnections()

	return nil
}

// Stop 停止服务器
func (s *Server) Stop() error {
	log.Printf("🛑 正在停止服务器...")

	s.cancel()

	if s.listener != nil {
		s.listener.Close()
	}

	// 等待所有goroutine结束，但设置超时避免无限等待
	done := make(chan struct{})
	go func() {
		s.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		log.Printf("✅ 所有goroutine已正常结束")
	case <-time.After(10 * time.Second):
		log.Printf("⚠️  等待goroutine超时，强制退出")
	}

	// 触发服务器停止事件
	s.eventLogger.ServerStop()

	// 输出最终统计信息
	s.printFinalStats()

	log.Printf("✅ 服务器已停止")
	return nil
}

// acceptConnections 接受客户端连接
func (s *Server) acceptConnections() {
	defer s.wg.Done()

	for {
		select {
		case <-s.ctx.Done():
			return
		default:
		}

		// 接受QUIC连接
		conn, err := s.listener.Accept(s.ctx)
		if err != nil {
			select {
			case <-s.ctx.Done():
				return
			default:
				log.Printf("❌ 接受连接失败: %v", err)
				s.eventLogger.ServerError(err)
				continue
			}
		}

		atomic.AddInt64(&s.totalQUICConnections, 1)
		atomic.AddInt64(&s.activeQUICConnections, 1)

		log.Printf("🔗 新QUIC连接: %s", conn.RemoteAddr())

		// 检查是否使用了0-RTT
		if s.config.EnableDebug {
			connState := conn.ConnectionState()
			if connState.TLS.DidResume {
				log.Printf("🚀 连接使用了会话恢复: %s", conn.RemoteAddr())
			}
			if connState.Used0RTT {
				log.Printf("⚡ 连接成功使用0-RTT: %s", conn.RemoteAddr())
			}
		}

		// 触发客户端连接事件
		s.eventLogger.ClientConnect(conn.RemoteAddr())

		// 创建QUIC连接处理器
		quicConn := NewQUICConnectionHandler(s, conn)

		// 启动连接处理
		if err := quicConn.Start(); err != nil {
			log.Printf("❌ QUIC连接启动失败: %v", err)
			// 如果是连接限制错误，记录拒绝原因
			if err.Error() == "注册连接失败: max connections reached" {
				log.Printf("⚠️  达到全局最大连接数限制，拒绝连接: %s", conn.RemoteAddr())
			}
			conn.CloseWithError(0, "connection rejected")
			return
		}

		// 处理连接（每个QUIC连接可以有多个Stream）
		s.wg.Add(1)
		go s.handleConnectionWithHandler(quicConn)
	}
}

// handleAuthentication 处理连接认证
func (s *Server) handleAuthentication(quicConn *QUICConnectionHandler, authStream quic.Stream) bool {
	defer authStream.Close()

	// 设置认证超时
	authStream.SetReadDeadline(time.Now().Add(10 * time.Second))

	// 读取认证请求
	authReq, err := protocol.ReadAuthRequest(authStream)
	if err != nil {
		log.Printf("❌ 读取认证请求失败: %v", err)
		return false
	}

	// 触发认证请求事件
	s.eventLogger.AuthRequest(quicConn.GetClientAddr(), authReq.ClientId)

	// 执行认证
	authReq.Ip = quicConn.conn.RemoteAddr().String()

	authResp, err := s.authManager.AuthenticateConnection(quicConn, authReq)
	if err != nil {
		log.Printf("❌ 认证处理失败: %v", err)
		s.eventLogger.AuthFailure(quicConn.GetClientAddr(), authReq.ClientId, err.Error())
		return false
	}

	// 发送认证响应
	err = protocol.WriteAuthResponse(authStream, authResp)
	if err != nil {
		log.Printf("❌ 发送认证响应失败: %v", err)
		s.eventLogger.AuthFailure(quicConn.GetClientAddr(), authReq.ClientId, "Failed to send response")
		return false
	}

	success := authResp.Status == protocol.AuthStatusSuccess
	if success {
		s.eventLogger.AuthSuccess(quicConn.GetClientAddr(), authReq.ClientId)
	} else {
		s.eventLogger.AuthFailure(quicConn.GetClientAddr(), authReq.ClientId, authResp.Message)
	}

	return success
}

// StreamReadWriter 包装quic.Stream以实现io.ReadWriter接口
type StreamReadWriter struct {
	stream quic.Stream
}

func (srw *StreamReadWriter) Read(p []byte) (n int, err error) {
	return srw.stream.Read(p)
}

func (srw *StreamReadWriter) Write(p []byte) (n int, err error) {
	return srw.stream.Write(p)
}

// statsTask 统计任务
func (s *Server) statsTask() {
	defer s.wg.Done()

	ticker := time.NewTicker(s.config.StatsInterval)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.printStats()
		}
	}
}

// printStats 打印统计信息
func (s *Server) printStats() {
	uptime := time.Since(s.startTime)
	totalQUICConn := atomic.LoadInt64(&s.totalQUICConnections)
	activeQUICConn := atomic.LoadInt64(&s.activeQUICConnections)
	rejectedConn := atomic.LoadInt64(&s.rejectedConnections)
	totalStreams := atomic.LoadInt64(&s.totalStreams)
	activeStreams := atomic.LoadInt64(&s.activeStreams)
	uploaded := atomic.LoadInt64(&s.totalBytesUploaded)
	downloaded := atomic.LoadInt64(&s.totalBytesDownloaded)

	// 创建统计信息结构
	stats := &ServerStats{
		StartTime:            s.startTime,
		Uptime:               uptime,
		TotalConnections:     totalQUICConn,
		ActiveConnections:    activeQUICConn,
		RejectedConnections:  rejectedConn,
		TotalStreams:         totalStreams,
		ActiveStreams:        activeStreams,
		TotalBytesUploaded:   uploaded,
		TotalBytesDownloaded: downloaded,
	}

	// 触发统计更新事件
	s.eventLogger.StatsUpdate(stats)

	log.Printf("📊 === 服务器统计信息 ===")
	log.Printf("⏱️  运行时间: %v", uptime)
	log.Printf("🔗 QUIC连接 - 总计: %d, 活跃: %d, 拒绝: %d", totalQUICConn, activeQUICConn, rejectedConn)
	log.Printf("🎯 SOCKS Stream - 总计: %d, 活跃: %d", totalStreams, activeStreams)
	log.Printf("📈 数据传输 - 上传: %s, 下载: %s", formatBytes(uploaded), formatBytes(downloaded))

	// 打印用户统计
	s.printUserStats()

	log.Printf("========================")
}

// printUserStats 打印用户统计信息
func (s *Server) printUserStats() {
	authStats := s.authManager.GetStats()

	totalUsers := authStats["total_connections"].(int)
	if totalUsers == 0 {
		log.Printf("👥 当前无活跃用户")
		return
	}

	log.Printf("👥 活跃用户统计 (%d个):", totalUsers)

	connections := authStats["connections"].([]map[string]interface{})
	for _, conn := range connections {
		userID := conn["user_id"].(string)
		streamCount := conn["stream_count"].(int)
		bytesUp := conn["bytes_up"].(int64)
		bytesDown := conn["bytes_down"].(int64)
		authTime := time.Unix(conn["auth_time"].(int64), 0)
		duration := time.Since(authTime)

		log.Printf("  📱 %s - 连接时长: %v, Streams: %d, ⬆️ %s, ⬇️ %s",
			userID, duration, streamCount,
			formatBytes(bytesUp), formatBytes(bytesDown))
	}
}

// printFinalStats 打印最终统计信息
func (s *Server) printFinalStats() {
	uptime := time.Since(s.startTime)
	totalQUICConn := atomic.LoadInt64(&s.totalQUICConnections)
	rejectedConn := atomic.LoadInt64(&s.rejectedConnections)
	totalStreams := atomic.LoadInt64(&s.totalStreams)
	uploaded := atomic.LoadInt64(&s.totalBytesUploaded)
	downloaded := atomic.LoadInt64(&s.totalBytesDownloaded)

	log.Printf("📊 === Quic最终统计信息 ===")
	log.Printf("⏱️  总运行时间: %v", uptime)
	log.Printf("🔗 处理的QUIC连接总数: %d", totalQUICConn)
	log.Printf("🎯 处理的SOCKS Stream总数: %d", totalStreams)
	log.Printf("❌ 拒绝的连接数: %d", rejectedConn)
	log.Printf("📈 总数据传输 - 上传: %s, 下载: %s", formatBytes(uploaded), formatBytes(downloaded))
	log.Printf("⚡ 平均连接速率: %.2f 连接/分钟", float64(totalStreams)/uptime.Minutes())
	log.Printf("========================")
}

// GetStats 获取服务器统计信息
func (s *Server) GetStats() map[string]interface{} {
	uptime := time.Since(s.startTime)
	totalQUICConn := atomic.LoadInt64(&s.totalQUICConnections)
	activeQUICConn := atomic.LoadInt64(&s.activeQUICConnections)
	rejectedConn := atomic.LoadInt64(&s.rejectedConnections)
	totalStreams := atomic.LoadInt64(&s.totalStreams)
	activeStreams := atomic.LoadInt64(&s.activeStreams)
	uploaded := atomic.LoadInt64(&s.totalBytesUploaded)
	downloaded := atomic.LoadInt64(&s.totalBytesDownloaded)

	// 获取用户统计（从 AuthManager）
	authStats := s.authManager.GetStats()

	// 获取 TrafficManager 统计（如果可用）
	var trafficStats map[string]interface{}
	if s.trafficManager != nil {
		trafficStats = s.trafficManager.GetAllStats()
	}

	return map[string]interface{}{
		"uptime": uptime.Seconds(),
		"quic_connections": map[string]int64{
			"total":    totalQUICConn,
			"active":   activeQUICConn,
			"rejected": rejectedConn,
		},
		"streams": map[string]int64{
			"total":  totalStreams,
			"active": activeStreams,
		},
		"traffic": map[string]int64{
			"uploaded":   uploaded,
			"downloaded": downloaded,
		},
		"users":          authStats["connections"],
		"traffic_detail": trafficStats, // 新增：详细的流量统计
	}
}

// GetTrafficStats 获取详细的流量统计信息
func (s *Server) GetTrafficStats() map[string]interface{} {
	if s.trafficManager != nil {
		return s.trafficManager.GetAllStats()
	}
	return map[string]interface{}{}
}

// handleConnectionWithHandler 处理QUICConnectionHandler包装的连接
func (s *Server) handleConnectionWithHandler(quicConn *QUICConnectionHandler) {
	connectTime := time.Now() // 记录连接开始时间
	defer s.wg.Done()
	defer func() {
		atomic.AddInt64(&s.activeQUICConnections, -1)

		// 关闭连接处理器
		quicConn.Close()

		// 触发客户端断开事件
		duration := time.Since(connectTime)
		s.eventLogger.ClientDisconnect(quicConn.conn.RemoteAddr(), duration)

		log.Printf("👋 移除用户连接: %s", quicConn.conn.RemoteAddr())
	}()

	// 获取底层QUIC连接
	conn := quicConn.GetQUICConnection()
	log.Printf("🔄 开始处理QUIC连接: %s", conn.RemoteAddr())

	// 创建带超时的上下文，防止连接卡住
	connCtx, connCancel := context.WithTimeout(s.ctx, 30*time.Second)
	defer connCancel()

	// 第一步：等待认证Stream（Stream ID 0用于认证）
	authStream, err := conn.AcceptStream(connCtx)
	if err != nil {
		log.Printf("❌ 等待认证Stream失败: %v", err)
		return
	}

	// 处理认证
	if !s.handleAuthentication(quicConn, authStream) {
		log.Printf("❌ 连接认证失败: %s", conn.RemoteAddr())
		return
	}

	log.Printf("✅ 连接认证成功: %s", conn.RemoteAddr())

	// 认证成功后，处理后续的SOCKS Stream
	// 创建一个管理Stream的WaitGroup，用于优雅关闭
	var streamWG sync.WaitGroup
	defer func() {
		// 等待所有Stream处理完成，但设置超时
		done := make(chan struct{})
		go func() {
			streamWG.Wait()
			close(done)
		}()

		select {
		case <-done:
			log.Printf("✅ 连接 %s 的所有Stream已正常结束", conn.RemoteAddr())
		case <-time.After(5 * time.Second):
			log.Printf("⚠️  连接 %s 的Stream等待超时", conn.RemoteAddr())
		}
	}()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-conn.Context().Done():
			return
		default:
		}

		// 接受新的Stream（每个Stream代表一个SOCKS连接）
		stream, err := conn.AcceptStream(s.ctx)
		if err != nil {
			select {
			case <-s.ctx.Done():
				return
			case <-conn.Context().Done():
				return
			default:
				log.Printf("❌ 接受Stream失败: %v", err)
				return
			}
		}

		log.Printf("📡 新Stream: %d", stream.StreamID())

		// 更新连接的流计数
		quicConn.AddStream()

		// 处理Stream（每个Stream是一个独立的SOCKS连接）
		streamWG.Add(1)
		s.wg.Add(1)
		go func(s *Server, quicConn *QUICConnectionHandler, stream quic.Stream) {
			defer streamWG.Done()
			defer quicConn.RemoveStream()
			s.handleSOCKSStreamWithHandler(quicConn, stream)
		}(s, quicConn, stream)
	}
}

// handleSOCKSStreamWithHandler 处理单个SOCKS Stream（使用QUICConnectionHandler）
func (s *Server) handleSOCKSStreamWithHandler(quicConn *QUICConnectionHandler, stream quic.Stream) {
	defer s.wg.Done()
	defer stream.Close()

	// 获取底层QUIC连接
	conn := quicConn.GetQUICConnection()
	streamID := stream.StreamID()
	log.Printf("🎯 开始处理SOCKS Stream: %d", streamID)

	// 统计Stream数量
	atomic.AddInt64(&s.totalStreams, 1)
	atomic.AddInt64(&s.activeStreams, 1)

	// 统一的错误处理和清理
	defer func() {
		atomic.AddInt64(&s.activeStreams, -1)
		// 通过token获取AuthState更新计数
		if authState := quicConn.GetAuthStateFromGlobal(); authState != nil {
			authState.StreamCount--
			authState.LastActivity = time.Now()
		}
		log.Printf("🔚 SOCKS Stream处理结束: %d", streamID)
	}()

	// 通过token获取AuthState更新计数
	if authState := quicConn.GetAuthStateFromGlobal(); authState != nil {
		authState.StreamCount++
		authState.LastActivity = time.Now()
	}

	// 读取连接请求
	connectReq, err := protocol.ReadConnectRequest(stream)
	if err != nil {
		log.Printf("❌ [Stream %d] 读取连接请求失败: %v", streamID, err)
		protocol.WriteConnectResponse(stream, protocol.NewConnectResponse(false, "Failed to read request"))
		return
	}

	// 调试：打印详细的连接请求信息
	log.Printf("🔍 [Stream %d] ConnectRequest详情 - Address: '%s', Port: %d (0x%X), AddrType: %v",
		streamID, connectReq.Address, connectReq.Port, connectReq.Port, connectReq.AddrType)

	// 构造目标地址
	targetAddrStr := fmt.Sprintf("%s:%d", connectReq.Address, connectReq.Port)
	log.Printf("📋 [Stream %d] 连接请求: %s", streamID, targetAddrStr)

	// 触发TCP请求事件
	s.eventLogger.TCPRequest(conn.RemoteAddr(), targetAddrStr, uint64(streamID))

	// 创建SOCKS连接并连接到目标
	socksConn := NewSOCKSConnection(stream)
	connectStart := time.Now()
	if err := socksConn.ConnectToTarget(connectReq.Address, uint16(connectReq.Port)); err != nil {
		log.Printf("❌ [Stream %d] 连接目标服务器失败: %v", streamID, err)
		s.eventLogger.TCPError(conn.RemoteAddr(), targetAddrStr, uint64(streamID), err)
		protocol.WriteConnectResponse(stream, protocol.NewConnectResponse(false, fmt.Sprintf("Connection failed: %v", err)))
		return
	}
	connectDuration := time.Since(connectStart)

	// 触发TCP连接建立事件
	s.eventLogger.TCPConnect(conn.RemoteAddr(), targetAddrStr, uint64(streamID), connectDuration)

	// 发送成功响应
	if err := protocol.WriteConnectResponse(stream, protocol.NewConnectResponse(true, "")); err != nil {
		log.Printf("❌ [Stream %d] 发送连接响应失败: %v", streamID, err)
		return
	}

	log.Printf("✅ [Stream %d] 连接建立成功: %s", streamID, targetAddrStr)

	// 启动双向数据转发 - 使用事件驱动流量统计
	socksConn.StartForwarding(
		func(streamID quic.StreamID, uploaded, downloaded int64) {
			// 获取连接和用户信息
			connectionID := quicConn.GetConnID()
			authState := quicConn.GetAuthStateFromGlobal()
			userID := ""
			if authState != nil {
				userID = authState.UserID
			} else {
				log.Printf("❌ 无法更新流量，连接认证已失效，拒绝Stream: %d", streamID)
				return
			}

			// 通过 TrafficManager 更新统计（事件驱动，统一处理）
			streamIDStr := fmt.Sprintf("quic-%d", streamID)
			s.trafficManager.UpdateTraffic(TrafficTypeQUIC, streamIDStr, connectionID, authState.Token, userID, targetAddrStr, uploaded, downloaded)
		},
		func(streamID quic.StreamID, targetAddr string, uploaded, downloaded int64, duration time.Duration) {
			// 触发TCP关闭事件
			s.eventLogger.TCPClose(conn.RemoteAddr(), targetAddr, uint64(streamID), uploaded, downloaded, duration)
		},
	)
}
