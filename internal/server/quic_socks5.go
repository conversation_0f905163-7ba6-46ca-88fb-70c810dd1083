package server

import (
	"context"
	"fmt"
	"io"
	"log"
	"net"
	"sync"
	"sync/atomic"
	"time"

	"github.com/quic-go/quic-go"
)

// SOCKSConnection SOCKS连接封装
type SOCKSConnection struct {
	stream     quic.Stream
	targetConn net.Conn
	ctx        context.Context
	cancel     context.CancelFunc
	mu         sync.RWMutex
	closed     bool
	lastActive time.Time

	// 连接信息
	targetAddr string
	targetPort uint16

	// 统计信息
	bytesUploaded   int64
	bytesDownloaded int64
	startTime       time.Time
}

// NewSOCKSConnection 创建新的SOCKS连接
func NewSOCKSConnection(stream quic.Stream) *SOCKSConnection {
	ctx, cancel := context.WithCancel(context.Background())
	return &SOCKSConnection{
		stream:     stream,
		ctx:        ctx,
		cancel:     cancel,
		lastActive: time.Now(),
		startTime:  time.Now(),
	}
}

// GetStreamID 获取Stream ID
func (c *SOCKSConnection) GetStreamID() quic.StreamID {
	return c.stream.StreamID()
}

// IsClosed 检查连接是否已关闭
func (c *SOCKSConnection) IsClosed() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.closed
}

// UpdateLastActive 更新最后活跃时间
func (c *SOCKSConnection) UpdateLastActive() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.lastActive = time.Now()
}

// GetLastActive 获取最后活跃时间
func (c *SOCKSConnection) GetLastActive() time.Time {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.lastActive
}

// GetStats 获取连接统计信息
func (c *SOCKSConnection) GetStats() (uploaded, downloaded int64, duration time.Duration) {
	uploaded = atomic.LoadInt64(&c.bytesUploaded)
	downloaded = atomic.LoadInt64(&c.bytesDownloaded)
	duration = time.Since(c.startTime)
	return
}

// ConnectToTarget 连接到目标服务器
func (c *SOCKSConnection) ConnectToTarget(addr string, port uint16) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.closed {
		return fmt.Errorf("connection is closed")
	}

	target := fmt.Sprintf("%s:%d", addr, port)
	log.Printf("🔍 [Stream %d] ConnectToTarget - addr='%s', port=%d (0x%X)", c.stream.StreamID(), addr, port, port)
	log.Printf("[Stream %d] 正在连接到目标服务器: %s", c.stream.StreamID(), target)

	// 使用带超时的连接
	dialer := &net.Dialer{
		Timeout:   10 * time.Second,
		KeepAlive: 30 * time.Second,
	}

	conn, err := dialer.DialContext(c.ctx, "tcp", target)
	if err != nil {
		log.Printf("[Stream %d] 连接目标服务器失败: %v", c.stream.StreamID(), err)
		return fmt.Errorf("failed to connect to target %s: %w", target, err)
	}

	// 设置TCP连接参数
	if tcpConn, ok := conn.(*net.TCPConn); ok {
		tcpConn.SetKeepAlive(true)
		tcpConn.SetKeepAlivePeriod(30 * time.Second)
		tcpConn.SetNoDelay(true)
	}

	c.targetConn = conn
	c.targetAddr = addr
	c.targetPort = port

	log.Printf("[Stream %d] 成功连接到目标服务器: %s", c.stream.StreamID(), target)
	return nil
}

// StartForwardingWithEvents 启动双向数据转发
func (c *SOCKSConnection) StartForwarding(
	trafficCallback func(streamID quic.StreamID, uploaded, downloaded int64),
	closeCallback func(streamID quic.StreamID, targetAddr string, uploaded, downloaded int64, duration time.Duration),
) {
	defer func() {
		// 在关闭前触发关闭回调
		if closeCallback != nil {
			uploaded, downloaded, duration := c.GetStats()
			targetAddr := fmt.Sprintf("%s:%d", c.targetAddr, c.targetPort)
			closeCallback(c.stream.StreamID(), targetAddr, uploaded, downloaded, duration)
		}
		c.Close()
	}()

	if c.targetConn == nil {
		log.Printf("[Stream %d] 目标连接为空", c.stream.StreamID())
		return
	}

	log.Printf("[Stream %d] 开始双向数据转发", c.stream.StreamID())

	// 直接转发，不需要加密/解密
	done := make(chan struct{}, 2)

	// 客户端 -> 目标服务器
	go func() {
		defer func() { done <- struct{}{} }()
		c.forwardDirect(c.targetConn, c.stream, true, trafficCallback) // 传递实时统计回调
	}()

	// 目标服务器 -> 客户端
	go func() {
		defer func() { done <- struct{}{} }()
		c.forwardDirect(c.stream, c.targetConn, false, trafficCallback) // 传递实时统计回调
	}()

	// 等待任一方向完成
	<-done

	// 记录连接统计信息
	uploaded, downloaded, duration := c.GetStats()
	log.Printf("[Stream %d] 连接结束 - 上传: %d 字节, 下载: %d 字节, 持续时间: %v",
		c.stream.StreamID(), uploaded, downloaded, duration)
}

// forwardDirect 直接转发数据，不进行加密/解密 - 实时统计版本
func (c *SOCKSConnection) forwardDirect(dst io.Writer, src io.Reader, isUpload bool, trafficCallback func(streamID quic.StreamID, uploaded, downloaded int64)) {
	buffer := make([]byte, 32*1024)
	for {
		n, err := src.Read(buffer)
		if err != nil {
			if err != io.EOF {
				direction := "下载"
				if isUpload {
					direction = "上传"
				}
				log.Printf("❌ [Stream %d] %s数据读取失败: %v", c.GetStreamID(), direction, err)
			}
			return
		}

		if n > 0 {
			c.UpdateLastActive()

			// 直接写入，不进行加密/解密
			if _, err := dst.Write(buffer[:n]); err != nil {
				direction := "下载"
				if isUpload {
					direction = "上传"
				}
				log.Printf("❌ [Stream %d] %s数据写入失败: %v", c.GetStreamID(), direction, err)
				return
			}

			// 更新统计信息
			var uploaded, downloaded int64
			if isUpload {
				atomic.AddInt64(&c.bytesUploaded, int64(n))
				uploaded = int64(n)
				downloaded = 0
			} else {
				atomic.AddInt64(&c.bytesDownloaded, int64(n))
				uploaded = 0
				downloaded = int64(n)
			}

			// 实时回调流量更新
			if trafficCallback != nil {
				trafficCallback(c.stream.StreamID(), uploaded, downloaded)
			}
		}
	}
}

// Close 关闭连接
func (c *SOCKSConnection) Close() {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.closed {
		return
	}

	c.closed = true
	c.cancel()

	log.Printf("[Stream %d] 关闭SOCKS连接", c.stream.StreamID())

	// 关闭目标连接
	if c.targetConn != nil {
		c.targetConn.Close()
		c.targetConn = nil
	}

	// 关闭QUIC流
	c.stream.Close()
}
