package server

import (
	"log"
	"sync/atomic"
	"time"
)

// ServerTrafficHandler 服务器级流量统计处理器（直接引用Server统计字段）
type ServerTrafficHandler struct {
	server *Server // 直接引用Server实例
}

// NewServerTrafficHandler 创建服务器级流量统计处理器
func NewServerTrafficHandler(server *Server) *ServerTrafficHandler {
	return &ServerTrafficHandler{
		server: server,
	}
}

func (h *ServerTrafficHandler) HandleTraffic(event *TrafficEvent) {
	// 直接更新Server的统计字段
	atomic.AddInt64(&h.server.totalBytesUploaded, event.Uploaded)
	atomic.AddInt64(&h.server.totalBytesDownloaded, event.Downloaded)
}

func (h *ServerTrafficHandler) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"total_uploaded":   atomic.LoadInt64(&h.server.totalBytesUploaded),
		"total_downloaded": atomic.LoadInt64(&h.server.totalBytesDownloaded),
		"uptime_seconds":   time.Since(h.server.startTime).Seconds(),
		"start_time":       h.server.startTime.Unix(),
	}
}

func (h *ServerTrafficHandler) GetName() string {
	return "server_quic"
}

func (h *ServerTrafficHandler) Close() {
	// 服务器级处理器无需特殊清理
}

// HTTP2TrafficHandler HTTP2服务器级流量统计处理器
type HTTP2TrafficHandler struct {
	server *HTTP2Server // 直接引用HTTP2Server实例
}

// NewHTTP2TrafficHandler 创建HTTP2服务器级流量统计处理器
func NewHTTP2TrafficHandler(server *HTTP2Server) *HTTP2TrafficHandler {
	return &HTTP2TrafficHandler{
		server: server,
	}
}

func (h *HTTP2TrafficHandler) HandleTraffic(event *TrafficEvent) {
	// 直接更新HTTP2Server的统计字段
	atomic.AddInt64(&h.server.totalBytesUploaded, event.Uploaded)
	atomic.AddInt64(&h.server.totalBytesDownloaded, event.Downloaded)
}

func (h *HTTP2TrafficHandler) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"total_uploaded":   atomic.LoadInt64(&h.server.totalBytesUploaded),
		"total_downloaded": atomic.LoadInt64(&h.server.totalBytesDownloaded),
		"uptime_seconds":   time.Since(h.server.startTime).Seconds(),
		"start_time":       h.server.startTime.Unix(),
	}
}

func (h *HTTP2TrafficHandler) GetName() string {
	return "server_http2"
}

func (h *HTTP2TrafficHandler) Close() {
	// HTTP2服务器级处理器无需特殊清理
}

// UserTrafficHandler 用户级流量统计处理器（专注用户管理功能）
type UserTrafficHandler struct {
	authManager *AuthManager // 用户认证管理器
}

// NewUserTrafficHandler 创建用户级流量统计处理器
func NewUserTrafficHandler(authManager *AuthManager) *UserTrafficHandler {
	return &UserTrafficHandler{
		authManager: authManager,
	}
}

func (h *UserTrafficHandler) HandleTraffic(event *TrafficEvent) {
	if h.authManager != nil {
		h.authManager.UpdateStatsUnified(event)
	} else {
		log.Printf("⚠️  AuthManager未初始化，无法处理流量事件: ConnectionID=%s, TrafficType=%s",
			event.ConnectionID, event.TrafficType)
	}
}

func (h *UserTrafficHandler) GetStats() map[string]interface{} {
	// 直接使用AuthManager的GetStats方法，避免破坏封装性
	if h.authManager != nil {
		authStats := h.authManager.GetStats()

		// 重新组织数据格式以匹配UserTrafficHandler的预期输出
		if connections, ok := authStats["connections"].([]map[string]interface{}); ok {
			users := make([]map[string]interface{}, 0, len(connections))
			totalBytesUp := int64(0)
			totalBytesDown := int64(0)

			for _, connStats := range connections {
				// 转换为用户统计格式
				userStats := map[string]interface{}{
					"connection_id": connStats["connection_id"],
					"user_id":       connStats["user_id"],
					"client_id":     connStats["client_id"],
					"auth_time":     connStats["auth_time"],
					"expires_at":    connStats["expires_at"],
					"last_activity": connStats["last_activity"],
					"stream_count":  connStats["stream_count"],
					"bytes_up":      connStats["bytes_up"],
					"bytes_down":    connStats["bytes_down"],
				}

				users = append(users, userStats)

				// 累计流量统计
				if bytesUp, ok := connStats["bytes_up"].(int64); ok {
					totalBytesUp += bytesUp
				}
				if bytesDown, ok := connStats["bytes_down"].(int64); ok {
					totalBytesDown += bytesDown
				}
			}

			return map[string]interface{}{
				"total_users":      len(users),
				"total_bytes_up":   totalBytesUp,
				"total_bytes_down": totalBytesDown,
				"users":            users,
			}
		}
	}

	// 如果AuthManager未初始化或获取失败，返回空统计
	return map[string]interface{}{
		"total_users":      0,
		"total_bytes_up":   int64(0),
		"total_bytes_down": int64(0),
		"users":            []map[string]interface{}{},
	}
}

func (h *UserTrafficHandler) GetName() string {
	return "user"
}

func (h *UserTrafficHandler) Close() {
	// 用户级处理器无需特殊清理，AuthManager 自己管理生命周期
}

// GetUserByToken 通过token获取用户信息
func (h *UserTrafficHandler) GetUserByToken(token string) *AuthState {
	if h.authManager != nil {
		return h.authManager.GetAuthState(token)
	}
	return nil
}

// GetOnlineUserCount 获取在线用户数量
func (h *UserTrafficHandler) GetOnlineUserCount() int {
	if h.authManager != nil {
		return h.authManager.GetOnlineTotalUsers()
	}
	return 0
}

// GetUserStats 获取指定用户的统计信息
func (h *UserTrafficHandler) GetUserStats(token string) map[string]interface{} {
	if authState := h.GetUserByToken(token); authState != nil {
		stats := map[string]interface{}{
			"user_id":       authState.UserID,
			"client_id":     authState.ClientID,
			"ip":            authState.Ip,
			"auth_time":     authState.AuthTime.Unix(),
			"last_activity": authState.LastActivity.Unix(),
			"stream_count":  authState.StreamCount,
			"bytes_up":      atomic.LoadInt64(&authState.BytesUp),
			"bytes_down":    atomic.LoadInt64(&authState.BytesDown),
			"is_auth":       authState.IsAuth,
		}

		// 添加连接信息
		if quicConn := authState.GetQUICConnectionHandler(); quicConn != nil {
			stats["connection_id"] = quicConn.GetConnID()
			stats["connection_type"] = "QUIC"
		} else {
			stats["connection_type"] = "HTTP2"
		}

		return stats
	}
	return nil
}

type TotalTrafficHandler struct {
	server *MultiProtocolServer
}

func NewTotalTrafficHandler(server *MultiProtocolServer) *TotalTrafficHandler {
	return &TotalTrafficHandler{
		server: server,
	}
}

func (h *TotalTrafficHandler) HandleTraffic(event *TrafficEvent) {
	atomic.AddInt64(&h.server.bytesUp, event.Uploaded)
	atomic.AddInt64(&h.server.bytesDown, event.Downloaded)
}

func (h *TotalTrafficHandler) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"total_uploaded":   atomic.LoadInt64(&h.server.bytesUp),
		"total_downloaded": atomic.LoadInt64(&h.server.bytesDown),
	}
}

func (h *TotalTrafficHandler) GetName() string {
	return "total"
}

func (h *TotalTrafficHandler) Close() {
	// 总流量处理器无需特殊清理
}
