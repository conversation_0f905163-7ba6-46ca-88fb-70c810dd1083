package test

import (
	"context"
	"crypto/tls"
	"fmt"
	"log"
	"net"
	"testing"
	"time"

	"socks5-quic-server/internal/protocol"
	"socks5-quic-server/internal/protocol/pb"
	"socks5-quic-server/internal/server"

	quicgo "github.com/quic-go/quic-go"
	"google.golang.org/protobuf/proto"
)

// TestAuthFailureHandling 测试认证失败的处理
func TestAuthFailureHandling(t *testing.T) {
	// 启动测试服务器
	serverAddr := "127.0.0.1:18443"
	srv := startTestServer(t, serverAddr)
	defer srv.Stop()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)

	// 测试场景1：使用无效token
	t.Run("InvalidToken", func(t *testing.T) {
		testInvalidTokenAuth(t, serverAddr)
	})

	// 测试场景2：使用空token
	t.Run("EmptyToken", func(t *testing.T) {
		testEmptyTokenAuth(t, serverAddr)
	})

	// 测试场景3：使用过期token
	t.Run("ExpiredToken", func(t *testing.T) {
		testExpiredTokenAuth(t, serverAddr)
	})
}

// startTestServer 启动测试服务器
func startTestServer(t *testing.T, addr string) *server.Server {
	// 创建认证管理器
	authConfig := &server.AuthConfig{
		Mode:      "token", // 使用token模式便于测试
		Timeout:   10,
		CacheSize: 100,
		CacheTTL:  300,
	}
	authManager := server.NewAuthManager(authConfig)

	// 创建流量管理器
	trafficManager := server.NewTrafficManager(1000)

	// 创建连接管理器
	connectionManager := server.NewConnectionManager(100, 30*time.Second)

	// 创建事件日志器
	eventLogger := server.NewDefaultEventLogger(true)

	// 生成自签名证书
	cert, err := generateTestCert()
	if err != nil {
		t.Fatalf("生成测试证书失败: %v", err)
	}

	// 创建服务器配置
	config := &server.Config{
		ListenAddr:      addr,
		TLSConfig:       &tls.Config{Certificates: []tls.Certificate{cert}},
		MaxConnections:  100,
		IdleTimeout:     30 * time.Second,
		CleanupInterval: 10 * time.Second,
		StatsInterval:   60 * time.Second,
		AuthConfig:      authConfig,
		EnableDebug:     true,
	}

	// 创建服务器
	srv, err := server.NewServer(config, authManager, trafficManager, connectionManager, eventLogger)
	if err != nil {
		t.Fatalf("创建服务器失败: %v", err)
	}

	// 启动服务器
	go func() {
		if err := srv.Start(); err != nil {
			log.Printf("服务器启动失败: %v", err)
		}
	}()

	return srv
}

// testInvalidTokenAuth 测试无效token认证
func testInvalidTokenAuth(t *testing.T, serverAddr string) {
	invalidToken := "invalid-token-12345"
	clientID := "test-client"

	// 创建QUIC连接
	conn, err := connectToServer(serverAddr)
	if err != nil {
		t.Fatalf("连接服务器失败: %v", err)
	}
	defer conn.CloseWithError(0, "test completed")

	// 执行认证
	authResult, err := performAuth(conn, invalidToken, clientID)
	if err != nil {
		t.Fatalf("认证过程失败: %v", err)
	}

	// 验证结果
	if authResult.Status == pb.AuthStatus_AUTH_SUCCESS {
		t.Errorf("预期认证失败，但实际成功了")
	}

	if authResult.Status != pb.AuthStatus_AUTH_FAILED {
		t.Errorf("预期认证状态为AUTH_FAILED，实际为: %v", authResult.Status)
	}

	t.Logf("✅ 无效token认证测试通过: %s", authResult.Message)
}

// testEmptyTokenAuth 测试空token认证
func testEmptyTokenAuth(t *testing.T, serverAddr string) {
	emptyToken := ""
	clientID := "test-client"

	// 创建QUIC连接
	conn, err := connectToServer(serverAddr)
	if err != nil {
		t.Fatalf("连接服务器失败: %v", err)
	}
	defer conn.CloseWithError(0, "test completed")

	// 执行认证
	authResult, err := performAuth(conn, emptyToken, clientID)
	if err != nil {
		t.Fatalf("认证过程失败: %v", err)
	}

	// 验证结果
	if authResult.Status == pb.AuthStatus_AUTH_SUCCESS {
		t.Errorf("预期认证失败，但实际成功了")
	}

	t.Logf("✅ 空token认证测试通过: %s", authResult.Message)
}

// testExpiredTokenAuth 测试过期token认证
func testExpiredTokenAuth(t *testing.T, serverAddr string) {
	expiredToken := "eyJ0eXAiOiJqd3QifQ.eyJzdWIiOiIxIiwiaXNzIjoiaHR0cDpcL1wvOiIsImV4cCI6MTUwMDAwMDAwMCwiaWF0IjoxNTAwMDAwMDAwLCJuYmYiOjE1MDAwMDAwMDAsInVpZCI6MSwicyI6IlhQVEsyZSIsImp0aSI6ImNhMDI5OGNmYTgzNjlmOTNlNjU2NzMxNTQyYzQ3OTgyIn0.expired"
	clientID := "test-client"

	// 创建QUIC连接
	conn, err := connectToServer(serverAddr)
	if err != nil {
		t.Fatalf("连接服务器失败: %v", err)
	}
	defer conn.CloseWithError(0, "test completed")

	// 执行认证
	authResult, err := performAuth(conn, expiredToken, clientID)
	if err != nil {
		t.Fatalf("认证过程失败: %v", err)
	}

	// 验证结果
	if authResult.Status == pb.AuthStatus_AUTH_SUCCESS {
		t.Errorf("预期认证失败，但实际成功了")
	}

	t.Logf("✅ 过期token认证测试通过: %s", authResult.Message)
}

// connectToServer 连接到服务器
func connectToServer(serverAddr string) (quicgo.Connection, error) {
	host, _, err := net.SplitHostPort(serverAddr)
	if err != nil {
		return nil, fmt.Errorf("invalid server address: %w", err)
	}

	tlsConfig := &tls.Config{
		ServerName:         host,
		InsecureSkipVerify: true,
		NextProtos:         []string{"socks5-quic"},
	}

	quicConfig := &quicgo.Config{
		MaxIdleTimeout:        30 * time.Second,
		MaxIncomingStreams:    100,
		MaxIncomingUniStreams: 100,
		KeepAlivePeriod:       10 * time.Second,
	}

	return quicgo.DialAddr(context.Background(), serverAddr, tlsConfig, quicConfig)
}

// performAuth 执行认证
func performAuth(conn quicgo.Connection, token, clientID string) (*pb.AuthResponse, error) {
	// 创建认证流
	stream, err := conn.OpenStreamSync(context.Background())
	if err != nil {
		return nil, fmt.Errorf("创建认证流失败: %v", err)
	}
	defer stream.Close()

	// 构造认证请求
	authReq := &pb.AuthRequest{
		Version:   1,
		Token:     token,
		ClientId:  clientID,
		Timestamp: uint64(time.Now().Unix()),
		Nonce:     uint32(time.Now().UnixNano() % 0xFFFFFFFF),
	}

	// 序列化认证请求
	data, err := proto.Marshal(authReq)
	if err != nil {
		return nil, fmt.Errorf("序列化认证请求失败: %v", err)
	}

	// 发送认证请求
	if err := protocol.WriteMessage(stream, data); err != nil {
		return nil, fmt.Errorf("发送认证请求失败: %v", err)
	}

	// 读取认证响应
	respData, err := protocol.ReadMessage(stream)
	if err != nil {
		return nil, fmt.Errorf("读取认证响应失败: %v", err)
	}

	// 解析认证响应
	var authResp pb.AuthResponse
	if err := proto.Unmarshal(respData, &authResp); err != nil {
		return nil, fmt.Errorf("解析认证响应失败: %v", err)
	}

	return &authResp, nil
}

// generateTestCert 生成测试用的自签名证书
func generateTestCert() (tls.Certificate, error) {
	// 这里简化处理，实际应该生成真正的证书
	// 为了测试目的，我们使用一个简单的证书生成方法
	cert, err := tls.LoadX509KeyPair("testdata/test.crt", "testdata/test.key")
	if err != nil {
		// 如果没有测试证书文件，生成一个临时的
		return generateSelfSignedCert()
	}
	return cert, nil
}

// generateSelfSignedCert 生成自签名证书（简化版本）
func generateSelfSignedCert() (tls.Certificate, error) {
	// 这里应该实现真正的证书生成逻辑
	// 为了简化测试，我们返回一个空证书
	// 在实际测试中，需要实现完整的证书生成逻辑
	return tls.Certificate{}, fmt.Errorf("需要实现证书生成逻辑")
}

// startBenchServer 启动性能测试服务器
func startBenchServer(b *testing.B, addr string) *server.Server {
	// 创建认证管理器
	authConfig := &server.AuthConfig{
		Mode:      "token", // 使用token模式便于测试
		Timeout:   10,
		CacheSize: 100,
		CacheTTL:  300,
	}
	authManager := server.NewAuthManager(authConfig)

	// 创建流量管理器
	trafficManager := server.NewTrafficManager(1000)

	// 创建连接管理器
	connectionManager := server.NewConnectionManager(100, 30*time.Second)

	// 创建事件日志器
	eventLogger := server.NewDefaultEventLogger(false) // benchmark时关闭详细日志

	// 生成自签名证书
	cert, err := generateTestCert()
	if err != nil {
		b.Fatalf("生成测试证书失败: %v", err)
	}

	// 创建服务器配置
	config := &server.Config{
		ListenAddr:      addr,
		TLSConfig:       &tls.Config{Certificates: []tls.Certificate{cert}},
		MaxConnections:  100,
		IdleTimeout:     30 * time.Second,
		CleanupInterval: 10 * time.Second,
		StatsInterval:   60 * time.Second,
		AuthConfig:      authConfig,
		EnableDebug:     false, // benchmark时关闭debug
	}

	// 创建服务器
	srv, err := server.NewServer(config, authManager, trafficManager, connectionManager, eventLogger)
	if err != nil {
		b.Fatalf("创建服务器失败: %v", err)
	}

	// 启动服务器
	go func() {
		if err := srv.Start(); err != nil {
			log.Printf("服务器启动失败: %v", err)
		}
	}()

	return srv
}

// BenchmarkAuthFailure 认证失败性能测试
func BenchmarkAuthFailure(b *testing.B) {
	// 启动测试服务器
	serverAddr := "127.0.0.1:18444"
	srv := startBenchServer(b, serverAddr)
	defer srv.Stop()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			// 创建连接
			conn, err := connectToServer(serverAddr)
			if err != nil {
				b.Fatalf("连接服务器失败: %v", err)
			}

			// 执行认证
			_, err = performAuth(conn, "invalid-token", "bench-client")
			if err != nil {
				b.Fatalf("认证过程失败: %v", err)
			}

			conn.CloseWithError(0, "benchmark completed")
		}
	})
}

// TestAuthFailureTimeout 测试认证失败的超时处理
func TestAuthFailureTimeout(t *testing.T) {
	// 启动测试服务器
	serverAddr := "127.0.0.1:18445"
	srv := startTestServer(t, serverAddr)
	defer srv.Stop()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)

	// 创建连接
	conn, err := connectToServer(serverAddr)
	if err != nil {
		t.Fatalf("连接服务器失败: %v", err)
	}
	defer conn.CloseWithError(0, "test completed")

	// 记录开始时间
	startTime := time.Now()

	// 执行认证
	_, err = performAuth(conn, "invalid-token", "timeout-test-client")
	if err != nil {
		t.Fatalf("认证过程失败: %v", err)
	}

	// 检查耗时
	duration := time.Since(startTime)
	t.Logf("认证失败处理耗时: %v", duration)

	// 验证延迟时间是否在合理范围内（应该包含我们添加的延迟）
	if duration < 100*time.Millisecond {
		t.Errorf("认证失败处理时间过短，可能没有正确处理延迟: %v", duration)
	}

	if duration > 1*time.Second {
		t.Errorf("认证失败处理时间过长: %v", duration)
	}
}
