package test

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/pem"
	"fmt"
	"log"
	"math/big"
	"net"
	"testing"
	"time"

	"socks5-quic-server/internal/protocol/pb"
	"socks5-quic-server/internal/server"
)

// TestRealAPIAuthFailure 使用真实API服务器测试认证失败
func TestRealAPIAuthFailure(t *testing.T) {
	// 启动测试服务器（使用真实API配置）
	serverAddr := "127.0.0.1:18446"
	srv := startRealAPITestServer(t, serverAddr)
	defer srv.Stop()

	// 等待服务器启动
	time.Sleep(200 * time.Millisecond)

	// 测试无效token认证
	t.Run("InvalidTokenAuth", func(t *testing.T) {
		testRealAPIInvalidToken(t, serverAddr)
	})

	// 测试空token认证
	t.Run("EmptyTokenAuth", func(t *testing.T) {
		testRealAPIEmptyToken(t, serverAddr)
	})

	// 测试格式错误的token
	t.Run("MalformedTokenAuth", func(t *testing.T) {
		testRealAPIMalformedToken(t, serverAddr)
	})
}

// startRealAPITestServer 启动使用真实API的测试服务器
func startRealAPITestServer(t *testing.T, addr string) *server.MultiProtocolServer {
	// 创建认证配置 - 使用真实的API
	authConfig := &server.AuthConfig{
		Mode:                      "api",
		APIUrl:                    "http://*************:29501", // 真实API地址
		APIKey:                    "ewefjlw---ejfl.0.,,,,,,",    // 真实API密钥
		Timeout:                   10,
		CacheSize:                 100,
		CacheTTL:                  300,
		UserTrafficReportInterval: 1 * time.Minute,
	}

	// 生成自签名证书
	cert, err := generateSelfSignedCert()
	if err != nil {
		t.Fatalf("生成测试证书失败: %v", err)
	}

	// 创建多协议服务器配置
	config := &server.MultiProtocolConfig{
		MaxConnections:  100,
		IdleTimeout:     30 * time.Second,
		CleanupInterval: 10 * time.Second,
		StatsInterval:   60 * time.Second,
		AuthConfig:      authConfig,
		EnableDebug:     true,

		// QUIC配置
		QUICEnabled:    true,
		QUICListenAddr: addr,
		QUICTLSConfig:  &tls.Config{Certificates: []tls.Certificate{cert}},

		// HTTP2配置
		HTTP2Enabled:    false, // 只测试QUIC
		HTTP2ListenAddr: "",
		HTTP2TLSConfig:  nil,
		HTTP2MaxStreams: 20,
	}

	// 创建服务器
	srv, err := server.NewMultiProtocolServer(config)
	if err != nil {
		t.Fatalf("创建多协议服务器失败: %v", err)
	}

	// 启动服务器
	go func() {
		if err := srv.Start(); err != nil {
			log.Printf("服务器启动失败: %v", err)
		}
	}()

	return srv
}

// testRealAPIInvalidToken 测试无效token认证
func testRealAPIInvalidToken(t *testing.T, serverAddr string) {
	invalidToken := "invalid-token-12345-this-should-fail"
	clientID := "test-client-invalid"

	log.Printf("🧪 开始测试无效token认证: %s", invalidToken)

	// 创建QUIC连接
	conn, err := connectToServer(serverAddr)
	if err != nil {
		t.Fatalf("连接服务器失败: %v", err)
	}
	defer conn.CloseWithError(0, "test completed")

	// 记录开始时间
	startTime := time.Now()

	// 执行认证
	authResult, err := performAuth(conn, invalidToken, clientID)
	if err != nil {
		// 这里不应该有连接级别的错误
		t.Fatalf("认证过程失败: %v", err)
	}

	// 记录耗时
	duration := time.Since(startTime)
	log.Printf("⏱️  认证失败处理耗时: %v", duration)

	// 验证结果
	if authResult.Status == pb.AuthStatus_AUTH_SUCCESS {
		t.Errorf("预期认证失败，但实际成功了")
	}

	// 验证错误信息
	log.Printf("📄 认证失败响应: Status=%v, Message=%s", authResult.Status, authResult.Message)

	// 检查是否包含HTTP API相关的错误信息
	if authResult.Message == "" {
		t.Errorf("认证失败但没有返回错误信息")
	}

	// 验证延迟时间是否在合理范围内（应该包含我们添加的延迟）
	if duration < 100*time.Millisecond {
		t.Errorf("认证失败处理时间过短，可能没有正确处理延迟: %v", duration)
	}

	if duration > 5*time.Second {
		t.Errorf("认证失败处理时间过长: %v", duration)
	}

	t.Logf("✅ 无效token认证测试通过: %s", authResult.Message)
}

// testRealAPIEmptyToken 测试空token认证
func testRealAPIEmptyToken(t *testing.T, serverAddr string) {
	emptyToken := ""
	clientID := "test-client-empty"

	log.Printf("🧪 开始测试空token认证")

	// 创建QUIC连接
	conn, err := connectToServer(serverAddr)
	if err != nil {
		t.Fatalf("连接服务器失败: %v", err)
	}
	defer conn.CloseWithError(0, "test completed")

	// 执行认证
	authResult, err := performAuth(conn, emptyToken, clientID)
	if err != nil {
		t.Fatalf("认证过程失败: %v", err)
	}

	// 验证结果
	if authResult.Status == pb.AuthStatus_AUTH_SUCCESS {
		t.Errorf("预期认证失败，但实际成功了")
	}

	log.Printf("📄 空token认证失败响应: Status=%v, Message=%s", authResult.Status, authResult.Message)

	t.Logf("✅ 空token认证测试通过: %s", authResult.Message)
}

// testRealAPIMalformedToken 测试格式错误的token
func testRealAPIMalformedToken(t *testing.T, serverAddr string) {
	malformedToken := "malformed.token.format.error"
	clientID := "test-client-malformed"

	log.Printf("🧪 开始测试格式错误token认证: %s", malformedToken)

	// 创建QUIC连接
	conn, err := connectToServer(serverAddr)
	if err != nil {
		t.Fatalf("连接服务器失败: %v", err)
	}
	defer conn.CloseWithError(0, "test completed")

	// 执行认证
	authResult, err := performAuth(conn, malformedToken, clientID)
	if err != nil {
		t.Fatalf("认证过程失败: %v", err)
	}

	// 验证结果
	if authResult.Status == pb.AuthStatus_AUTH_SUCCESS {
		t.Errorf("预期认证失败，但实际成功了")
	}

	log.Printf("📄 格式错误token认证失败响应: Status=%v, Message=%s", authResult.Status, authResult.Message)

	t.Logf("✅ 格式错误token认证测试通过: %s", authResult.Message)
}

// generateSelfSignedCert 生成自签名证书
func generateSelfSignedCert() (tls.Certificate, error) {
	// 生成RSA私钥
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return tls.Certificate{}, fmt.Errorf("生成RSA私钥失败: %w", err)
	}

	// 创建证书模板
	template := x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			Organization:  []string{"SOCKS5-QUIC-Server-Test"},
			Country:       []string{"CN"},
			Province:      []string{""},
			Locality:      []string{""},
			StreetAddress: []string{""},
			PostalCode:    []string{""},
		},
		NotBefore:             time.Now(),
		NotAfter:              time.Now().Add(24 * time.Hour), // 测试证书1天有效期
		KeyUsage:              x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
		BasicConstraintsValid: true,
		DNSNames:              []string{"localhost", "*.localhost"},
		IPAddresses:           []net.IP{net.IPv4(127, 0, 0, 1), net.IPv6loopback},
	}

	// 生成证书
	certDER, err := x509.CreateCertificate(rand.Reader, &template, &template, &privateKey.PublicKey, privateKey)
	if err != nil {
		return tls.Certificate{}, fmt.Errorf("创建证书失败: %w", err)
	}

	// 编码证书和私钥
	certPEM := pem.EncodeToMemory(&pem.Block{Type: "CERTIFICATE", Bytes: certDER})
	keyPEM := pem.EncodeToMemory(&pem.Block{Type: "RSA PRIVATE KEY", Bytes: x509.MarshalPKCS1PrivateKey(privateKey)})

	// 创建TLS证书
	cert, err := tls.X509KeyPair(certPEM, keyPEM)
	if err != nil {
		return tls.Certificate{}, fmt.Errorf("创建TLS证书失败: %w", err)
	}

	return cert, nil
}

// TestAPIConnectivity 测试API连接性
func TestAPIConnectivity(t *testing.T) {
	// 创建API客户端
	apiClient := server.NewAPIClient(
		"http://*************:29501",
		"ewefjlw---ejfl.0.,,,,,,",
		10*time.Second,
	)

	// 测试认证请求
	checkReq := &server.CheckWSLoginRequest{
		Ip:        "127.0.0.1",
		Token:     "invalid-test-token",
		ClientID:  "test-client",
		Timestamp: time.Now().Unix(),
		Nonce:     "12345",
		ServerID:  "test-server",
		Fd:        1,
	}

	log.Printf("🔗 测试API连接性...")

	apiResp, err := apiClient.CheckWSLogin(checkReq)
	if err != nil {
		t.Logf("⚠️  API请求失败（这是预期的）: %v", err)
		// 这是预期的，因为我们使用的是无效token
	} else {
		log.Printf("📄 API响应: Success=%v, Message=%s, UserID=%s",
			apiResp.Success, apiResp.Message, apiResp.UserID)

		if apiResp.Success {
			t.Errorf("使用无效token不应该认证成功")
		}
	}

	t.Logf("✅ API连接性测试完成")
}
