@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

echo 🧪 开始测试QUIC认证失败修复...

REM 编译服务端和客户端
echo 📦 编译代码...
go build -o bin/server.exe cmd/server/main.go
if errorlevel 1 (
    echo ❌ 服务端编译失败
    exit /b 1
)

go build -o bin/client.exe cmd/client/main.go
if errorlevel 1 (
    echo ❌ 客户端编译失败
    exit /b 1
)

echo ✅ 编译完成

REM 启动服务器（后台运行）
echo 🚀 启动测试服务器...
start /b "" bin\server.exe -listen :18443 -auth-mode token -debug > server_test.log 2>&1

REM 等待服务器启动
timeout /t 3 /nobreak > nul

echo ✅ 服务器启动完成

REM 测试场景1：使用无效token
echo 🔍 测试场景1: 无效token认证
echo quit | bin\client.exe -server 127.0.0.1:18443 -token "invalid-token-12345" -interactive > client_test1.log 2>&1

REM 检查客户端日志
findstr /c:"connection closed" client_test1.log > nul
if !errorlevel! equ 0 (
    echo ❌ 测试失败：仍然出现connection closed错误
    echo 客户端日志：
    type client_test1.log
    set FAILED=1
) else (
    echo ✅ 测试通过：没有出现connection closed错误
)

REM 测试场景2：使用空token
echo 🔍 测试场景2: 空token认证
echo quit | bin\client.exe -server 127.0.0.1:18443 -token "" -interactive > client_test2.log 2>&1

REM 检查客户端日志
findstr /c:"connection closed" client_test2.log > nul
if !errorlevel! equ 0 (
    echo ❌ 测试失败：仍然出现connection closed错误
    echo 客户端日志：
    type client_test2.log
    set FAILED=1
) else (
    echo ✅ 测试通过：没有出现connection closed错误
)

REM 检查认证失败响应
findstr /c:"认证失败" client_test1.log > nul
if !errorlevel! equ 0 (
    echo ✅ 测试通过：客户端正确收到认证失败响应
) else (
    echo ⚠️  注意：没有找到认证失败响应信息
)

REM 清理
echo 🧹 清理测试环境...
taskkill /f /im server.exe > nul 2>&1

REM 清理日志文件
del /q server_test.log client_test1.log client_test2.log > nul 2>&1

if "!FAILED!" == "1" (
    echo ❌ 测试失败
    exit /b 1
) else (
    echo 🎉 所有测试通过！认证失败修复成功
    exit /b 0
) 