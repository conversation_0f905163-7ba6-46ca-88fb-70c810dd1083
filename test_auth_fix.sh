#!/bin/bash

# QUIC认证失败修复测试脚本

echo "🧪 开始测试QUIC认证失败修复..."

# 编译服务端和客户端
echo "📦 编译代码..."
go build -o bin/server.exe cmd/server/main.go
if [ $? -ne 0 ]; then
    echo "❌ 服务端编译失败"
    exit 1
fi

go build -o bin/client.exe cmd/client/main.go
if [ $? -ne 0 ]; then
    echo "❌ 客户端编译失败"
    exit 1
fi

echo "✅ 编译完成"

# 启动服务器（后台运行）
echo "🚀 启动测试服务器..."
./bin/server.exe -listen :18443 -auth-mode token -debug > server_test.log 2>&1 &
SERVER_PID=$!

# 等待服务器启动
sleep 2

# 检查服务器是否启动成功
if ! kill -0 $SERVER_PID 2>/dev/null; then
    echo "❌ 服务器启动失败"
    cat server_test.log
    exit 1
fi

echo "✅ 服务器启动成功 (PID: $SERVER_PID)"

# 测试场景1：使用无效token
echo "🔍 测试场景1: 无效token认证"
./bin/client.exe -server 127.0.0.1:18443 -token "invalid-token-12345" -interactive > client_test1.log 2>&1 &
CLIENT_PID=$!

# 等待客户端完成
sleep 3

# 检查客户端日志
if grep -q "connection closed" client_test1.log; then
    echo "❌ 测试失败：仍然出现connection closed错误"
    echo "客户端日志："
    cat client_test1.log
    FAILED=1
else
    echo "✅ 测试通过：没有出现connection closed错误"
fi

# 测试场景2：使用空token
echo "🔍 测试场景2: 空token认证"
./bin/client.exe -server 127.0.0.1:18443 -token "" -interactive > client_test2.log 2>&1 &
CLIENT_PID=$!

# 等待客户端完成
sleep 3

# 检查客户端日志
if grep -q "connection closed" client_test2.log; then
    echo "❌ 测试失败：仍然出现connection closed错误"
    echo "客户端日志："
    cat client_test2.log
    FAILED=1
else
    echo "✅ 测试通过：没有出现connection closed错误"
fi

# 清理
echo "🧹 清理测试环境..."
kill $SERVER_PID 2>/dev/null
wait $SERVER_PID 2>/dev/null

# 清理日志文件
rm -f server_test.log client_test1.log client_test2.log

if [ "$FAILED" = "1" ]; then
    echo "❌ 测试失败"
    exit 1
else
    echo "🎉 所有测试通过！认证失败修复成功"
    exit 0
fi 