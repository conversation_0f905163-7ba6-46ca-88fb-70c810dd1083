package main

import (
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net"
	"net/http"
	"strings"
	"time"

	"socks5-quic-server/internal/protocol"
	"socks5-quic-server/internal/protocol/pb"

	"github.com/quic-go/quic-go"
	"golang.org/x/net/http2"
	"google.golang.org/protobuf/proto"
)

func main() {
	fmt.Println("🧪 开始客户端认证测试...")
	fmt.Println("================================")

	// 服务器地址（根据您的服务器配置）
	quicServerAddr := "127.0.0.1:18443"
	http2ServerAddr := "https://127.0.0.1:18443"

	fmt.Printf("🔍 QUIC服务器: %s\n", quicServerAddr)
	fmt.Printf("🔍 HTTP2服务器: %s\n", http2ServerAddr)

	// 测试QUIC协议认证
	fmt.Println("\n" + strings.Repeat("=", 50))
	fmt.Println("🚀 QUIC协议认证测试")
	fmt.Println(strings.Repeat("=", 50))

	fmt.Println("\n🔐 QUIC测试1: 无效token认证")
	testQUICAuth(quicServerAddr, "invalid-token-12345", "test-quic-client-1")

	fmt.Println("\n🔐 QUIC测试2: 空token认证")
	testQUICAuth(quicServerAddr, "", "test-quic-client-2")

	fmt.Println("\n🔐 QUIC测试3: 格式错误token认证")
	testQUICAuth(quicServerAddr, "malformed.token.format", "test-quic-client-3")

	// 测试HTTP2协议认证
	fmt.Println("\n" + strings.Repeat("=", 50))
	fmt.Println("🌐 HTTP2协议认证测试")
	fmt.Println(strings.Repeat("=", 50))

	// 创建HTTP2客户端
	http2Client := createHTTP2Client()

	fmt.Println("\n🔐 HTTP2测试1: 无效token认证")
	testHTTP2Auth(http2Client, http2ServerAddr, "invalid-token-12345", "test-http2-client-1")

	fmt.Println("\n🔐 HTTP2测试2: 空token认证")
	testHTTP2Auth(http2Client, http2ServerAddr, "", "test-http2-client-2")

	fmt.Println("\n🔐 HTTP2测试3: 格式错误token认证")
	testHTTP2Auth(http2Client, http2ServerAddr, "malformed.token.format", "test-http2-client-3")

	fmt.Println("\n" + strings.Repeat("=", 50))
	fmt.Println("✅ 所有协议测试完成！")
	fmt.Println(strings.Repeat("=", 50))
}

// testQUICAuth 测试QUIC认证
func testQUICAuth(serverAddr, token, clientID string) {
	fmt.Printf("🔗 连接到: %s\n", serverAddr)
	fmt.Printf("📝 Token: %s\n", maskToken(token))
	fmt.Printf("🆔 Client ID: %s\n", clientID)

	// 连接到服务器
	conn, err := connectToServer(serverAddr)
	if err != nil {
		fmt.Printf("❌ 连接失败: %v\n", err)
		return
	}
	defer conn.CloseWithError(0, "test completed")

	// 执行认证
	authResp, err := performAuth(conn, token, clientID)
	if err != nil {
		fmt.Printf("❌ 认证过程失败: %v\n", err)
		return
	}

	// 分析认证结果
	fmt.Printf("📋 认证状态: %s\n", authResp.Status.String())
	fmt.Printf("📄 响应消息: %s\n", authResp.Message)

	if authResp.Status == pb.AuthStatus_AUTH_SUCCESS {
		fmt.Printf("✅ 认证成功！用户ID: %s\n", authResp.UserId)
	} else {
		fmt.Printf("❌ 认证失败，原因: %s\n", authResp.Message)
	}
}

// createHTTP2Client 创建HTTP2客户端
func createHTTP2Client() *http.Client {
	// 创建TLS配置
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true,
		NextProtos:         []string{"h2"}, // 强制使用HTTP2
	}

	// 创建HTTP2传输
	transport := &http2.Transport{
		TLSClientConfig: tlsConfig,
	}

	return &http.Client{
		Transport: transport,
		Timeout:   10 * time.Second,
	}
}

// testHTTP2Auth 测试HTTP2认证
func testHTTP2Auth(client *http.Client, serverAddr, token, clientID string) {
	fmt.Printf("🔗 连接到: %s\n", serverAddr)
	fmt.Printf("📝 Token: %s\n", maskToken(token))
	fmt.Printf("🆔 Client ID: %s\n", clientID)

	// 根据HTTP2服务端实现，使用POST请求到/tunnel端点
	req, err := http.NewRequest("POST", serverAddr+"/tunnel", nil)
	if err != nil {
		fmt.Printf("❌ 创建请求失败: %v\n", err)
		return
	}

	// 设置HTTP2 SOCKS5协议要求的头部
	req.Header.Set("x-target", "httpbin.org:80") // 测试目标地址
	if token != "" {
		req.Header.Set("x-auth-token", token)
	}
	req.Header.Set("x-client-id", clientID)
	req.Header.Set("User-Agent", "SOCKS5-HTTP2-Test-Client/1.0")
	req.Header.Set("Content-Type", "application/octet-stream")

	// 发送请求
	fmt.Printf("📤 发送HTTP2认证请求...\n")
	start := time.Now()

	resp, err := client.Do(req)
	duration := time.Since(start)

	if err != nil {
		fmt.Printf("❌ 请求失败: %v\n", err)
		fmt.Printf("⏱️  耗时: %v\n", duration)
		return
	}
	defer resp.Body.Close()

	fmt.Printf("⏱️  请求耗时: %v\n", duration)
	fmt.Printf("📊 HTTP状态码: %d %s\n", resp.StatusCode, resp.Status)
	fmt.Printf("🌐 协议版本: %s\n", resp.Proto)

	// 检查响应头
	fmt.Println("📋 响应头信息:")
	for name, values := range resp.Header {
		for _, value := range values {
			fmt.Printf("   %s: %s\n", name, value)
		}
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ 读取响应体失败: %v\n", err)
	} else {
		bodyStr := string(body)
		if len(bodyStr) > 0 {
			fmt.Printf("📄 响应体: %s\n", bodyStr)
		} else {
			fmt.Printf("📄 响应体: (空)\n")
		}
	}

	// 分析认证结果
	analyzeHTTP2AuthResult(resp, string(body))
}

// analyzeHTTP2AuthResult 分析HTTP2认证结果
func analyzeHTTP2AuthResult(resp *http.Response, body string) {
	fmt.Println("🔍 认证结果分析:")

	switch resp.StatusCode {
	case 200:
		fmt.Println("   ✅ 认证成功 - 隧道建立")
	case 401:
		fmt.Println("   ❌ 认证失败 - 401 Unauthorized")

		// 检查认证失败的详细信息（根据HTTP2服务端实现）
		if authError := resp.Header.Get("X-Info"); authError != "" {
			fmt.Printf("   📄 认证错误信息(头部): %s\n", authError)
		}

		if wwwAuth := resp.Header.Get("WWW-Authenticate"); wwwAuth != "" {
			fmt.Printf("   🔐 认证方式: %s\n", wwwAuth)
		}

		if errorMsg := resp.Header.Get("X-Error-Message"); errorMsg != "" {
			fmt.Printf("   💬 错误消息(头部): %s\n", errorMsg)
		}

		// 检查响应体中的错误信息
		if body != "" {
			fmt.Printf("   📝 错误详情(响应体): %s\n", body)
		}

	case 400:
		fmt.Println("   ❌ 请求错误 - 400 Bad Request")
		if info := resp.Header.Get("X-Info"); info != "" {
			fmt.Printf("   💬 错误信息: %s\n", info)
		}
	case 403:
		fmt.Println("   ❌ 认证失败 - 403 Forbidden")
	case 502:
		fmt.Println("   ❌ 网关错误 - 502 Bad Gateway")
	case 500:
		fmt.Println("   ❌ 服务器内部错误 - 500 Internal Server Error")
	default:
		fmt.Printf("   ❓ 未知状态码: %d\n", resp.StatusCode)
	}

	// 检查是否是连接关闭错误
	if strings.Contains(body, "connection closed") ||
		strings.Contains(body, "Application error") {
		fmt.Println("   🚨 检测到连接关闭错误！这表明修复可能不完整")
	} else if resp.StatusCode == 401 {
		fmt.Println("   ✅ 正确接收到认证失败响应，没有连接关闭错误")
	}

	// 检查是否有认证失败的详细信息在头部
	if resp.StatusCode == 401 {
		if xInfo := resp.Header.Get("X-Info"); xInfo != "" {
			fmt.Printf("   🎯 **关键发现**: 认证失败消息在HTTP头部: %s\n", xInfo)
		} else {
			fmt.Println("   ⚠️  认证失败但未在X-Info头部找到详细信息")
		}
	}
}

// connectToServer 连接到QUIC服务器
func connectToServer(serverAddr string) (quic.Connection, error) {
	host, _, err := net.SplitHostPort(serverAddr)
	if err != nil {
		return nil, fmt.Errorf("invalid server address: %w", err)
	}

	tlsConfig := &tls.Config{
		ServerName:         host,
		InsecureSkipVerify: true,
		NextProtos:         []string{"socks5-quic"},
	}

	quicConfig := &quic.Config{
		MaxIdleTimeout:        10 * time.Second,
		MaxIncomingStreams:    100,
		MaxIncomingUniStreams: 100,
		KeepAlivePeriod:       5 * time.Second,
		HandshakeIdleTimeout:  5 * time.Second,
	}

	return quic.DialAddr(context.Background(), serverAddr, tlsConfig, quicConfig)
}

// performAuth 执行QUIC认证
func performAuth(conn quic.Connection, token, clientID string) (*pb.AuthResponse, error) {
	// 创建认证流
	stream, err := conn.OpenStreamSync(context.Background())
	if err != nil {
		return nil, fmt.Errorf("创建认证流失败: %v", err)
	}
	defer stream.Close()

	// 创建认证请求
	authReq := protocol.NewAuthRequest(token, clientID)
	authReq.Timestamp = uint64(time.Now().Unix())
	authReq.Nonce = uint32(time.Now().UnixNano() % 1000000)

	// 编码认证请求
	data, err := proto.Marshal(authReq)
	if err != nil {
		return nil, fmt.Errorf("编码认证请求失败: %v", err)
	}

	// 发送认证请求
	_, err = stream.Write(data)
	if err != nil {
		return nil, fmt.Errorf("发送认证请求失败: %v", err)
	}

	// 读取认证响应
	buffer := make([]byte, 1024)
	n, err := stream.Read(buffer)
	if err != nil {
		return nil, fmt.Errorf("读取认证响应失败: %v", err)
	}

	// 解码认证响应
	var authResp pb.AuthResponse
	err = proto.Unmarshal(buffer[:n], &authResp)
	if err != nil {
		return nil, fmt.Errorf("解码认证响应失败: %v", err)
	}

	return &authResp, nil
}

// maskToken 隐藏token的敏感部分
func maskToken(token string) string {
	if token == "" {
		return "(空token)"
	}
	if len(token) <= 8 {
		return token[:len(token)/2] + "***"
	}
	return token[:4] + "***" + token[len(token)-4:]
}
