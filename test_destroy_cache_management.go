package main

import (
	"fmt"
	"log"
	"time"

	"socks5-quic-server/internal/server"
)

func main() {
	log.Printf("🧪 测试销毁和缓存管理改进")

	// 1. 测试新的DestroyAuthState方法
	log.Printf("📋 测试1：DestroyAuthState方法测试")
	testDestroyAuthState()

	time.Sleep(1 * time.Second)

	// 2. 测试缓存立即清理
	log.Printf("📋 测试2：缓存立即清理测试")
	testImmediateCacheCleanup()

	time.Sleep(1 * time.Second)

	// 3. 测试懒删除兜底机制
	log.Printf("📋 测试3：懒删除兜底机制测试")
	testLazyDeletionFallback()

	time.Sleep(1 * time.Second)

	// 4. 测试RemoveAuthState重构
	log.Printf("📋 测试4：RemoveAuthState重构测试")
	testRemoveAuthStateRefactor()

	log.Printf("🎉 所有测试完成")
}

// 测试新的DestroyAuthState方法
func testDestroyAuthState() {
	// 创建AuthManager
	authManager := server.NewAuthManager(nil)

	// 创建AuthState
	authState := &server.AuthState{
		UserID:       "test-user-destroy",
		Token:        "test-token-destroy",
		ClientID:     "test-client",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
	}

	// 启动上报
	authState.StartReporting(500 * time.Millisecond)

	// 添加到缓存
	authManager.GetAuthCache().Store(authState.Token, authState)

	// 验证缓存中存在
	if cachedAuth := authManager.GetCachedAuthPublic(authState.Token); cachedAuth != nil {
		log.Printf("✅ 缓存添加成功：AuthState存在于缓存中")
	} else {
		log.Printf("❌ 缓存添加失败")
		return
	}

	// 使用新的DestroyAuthState方法
	log.Printf("🗑️  调用DestroyAuthState...")
	authManager.DestroyAuthState(authState)

	// 验证销毁状态
	if authState.IsDestroyed() {
		log.Printf("✅ 销毁状态正确：AuthState已销毁")
	} else {
		log.Printf("❌ 销毁状态错误：AuthState未销毁")
		return
	}

	// 验证缓存立即清理
	if cachedAuth := authManager.GetCachedAuthPublic(authState.Token); cachedAuth == nil {
		log.Printf("✅ 缓存立即清理成功：缓存中不存在已销毁的AuthState")
	} else {
		log.Printf("❌ 缓存立即清理失败：缓存中仍存在已销毁的AuthState")
	}

	// 验证重复调用保护
	log.Printf("🔄 测试重复调用保护...")
	authManager.DestroyAuthState(authState) // 应该被跳过
	log.Printf("✅ 重复调用保护测试完成")
}

// 测试缓存立即清理
func testImmediateCacheCleanup() {
	// 创建AuthManager
	authManager := server.NewAuthManager(nil)

	// 创建多个AuthState
	authStates := make([]*server.AuthState, 3)
	for i := 0; i < 3; i++ {
		authStates[i] = &server.AuthState{
			UserID:       fmt.Sprintf("test-user-immediate-%d", i),
			Token:        fmt.Sprintf("test-token-immediate-%d", i),
			ClientID:     "test-client",
			IsAuth:       true,
			AuthTime:     time.Now(),
			LastActivity: time.Now(),
		}
		authStates[i].StartReporting(200 * time.Millisecond)
		authManager.GetAuthCache().Store(authStates[i].Token, authStates[i])
	}

	// 验证所有都在缓存中
	cacheCount := 0
	authManager.GetAuthCache().Range(func(key, value interface{}) bool {
		cacheCount++
		return true
	})
	log.Printf("📊 缓存中AuthState数量: %d", cacheCount)

	// 销毁第一个和第三个
	log.Printf("🗑️  销毁第1个和第3个AuthState...")
	authManager.DestroyAuthState(authStates[0])
	authManager.DestroyAuthState(authStates[2])

	// 检查缓存状态
	cacheCountAfter := 0
	authManager.GetAuthCache().Range(func(key, value interface{}) bool {
		cacheCountAfter++
		return true
	})
	log.Printf("📊 销毁后缓存中AuthState数量: %d", cacheCountAfter)

	if cacheCountAfter == 1 {
		log.Printf("✅ 缓存立即清理测试通过：只剩下未销毁的AuthState")
	} else {
		log.Printf("❌ 缓存立即清理测试失败：缓存数量不正确")
	}

	// 清理剩余的
	authManager.DestroyAuthState(authStates[1])
}

// 测试懒删除兜底机制
func testLazyDeletionFallback() {
	// 创建AuthManager
	authManager := server.NewAuthManager(nil)

	// 创建AuthState
	authState := &server.AuthState{
		UserID:       "test-user-lazy",
		Token:        "test-token-lazy",
		ClientID:     "test-client",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
	}

	// 启动上报
	authState.StartReporting(300 * time.Millisecond)

	// 添加到缓存
	authManager.GetAuthCache().Store(authState.Token, authState)

	// 直接调用Destroy（不通过AuthManager）
	log.Printf("🗑️  直接调用authState.Destroy()（模拟遗漏场景）...")
	authState.Destroy()

	// 此时缓存中应该还有"僵尸"对象
	if _, exists := authManager.GetAuthCache().Load(authState.Token); exists {
		log.Printf("✅ 预期行为：缓存中仍存在已销毁的AuthState（僵尸对象）")
	} else {
		log.Printf("❌ 意外行为：缓存中不存在AuthState")
		return
	}

	// 通过getCachedAuth触发懒删除
	log.Printf("🔍 通过getCachedAuth触发懒删除...")
	cachedAuth := authManager.GetCachedAuthPublic(authState.Token)

	if cachedAuth == nil {
		log.Printf("✅ 懒删除兜底机制正常：getCachedAuth返回nil")
	} else {
		log.Printf("❌ 懒删除兜底机制失败：getCachedAuth仍返回对象")
		return
	}

	// 验证缓存已被清理
	if _, exists := authManager.GetAuthCache().Load(authState.Token); !exists {
		log.Printf("✅ 懒删除清理成功：僵尸对象已从缓存中移除")
	} else {
		log.Printf("❌ 懒删除清理失败：僵尸对象仍在缓存中")
	}
}

// 测试RemoveAuthState重构
func testRemoveAuthStateRefactor() {
	// 创建AuthManager
	authManager := server.NewAuthManager(nil)

	// 创建AuthState
	authState := &server.AuthState{
		UserID:       "test-user-remove",
		Token:        "test-token-remove",
		ClientID:     "test-client",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
	}

	// 启动上报
	authState.StartReporting(400 * time.Millisecond)

	// 添加到缓存
	authManager.GetAuthCache().Store(authState.Token, authState)

	// 验证初始状态
	if cachedAuth := authManager.GetCachedAuthPublic(authState.Token); cachedAuth != nil {
		log.Printf("✅ 初始状态正确：AuthState存在于缓存中")
	} else {
		log.Printf("❌ 初始状态错误")
		return
	}

	// 调用RemoveAuthState
	log.Printf("🧹 调用RemoveAuthState...")
	authManager.RemoveAuthState(authState)

	// 验证销毁状态
	if authState.IsDestroyed() {
		log.Printf("✅ RemoveAuthState销毁成功")
	} else {
		log.Printf("❌ RemoveAuthState销毁失败")
		return
	}

	// 验证缓存清理
	if cachedAuth := authManager.GetCachedAuthPublic(authState.Token); cachedAuth == nil {
		log.Printf("✅ RemoveAuthState缓存清理成功")
	} else {
		log.Printf("❌ RemoveAuthState缓存清理失败")
	}

	// 测试重复调用
	log.Printf("🔄 测试RemoveAuthState重复调用...")
	authManager.RemoveAuthState(authState) // 应该被跳过
	log.Printf("✅ RemoveAuthState重复调用保护测试完成")
}
