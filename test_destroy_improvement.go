package main

import (
	"log"
	"sync"
	"time"

	"socks5-quic-server/internal/server"
)

func main() {
	log.Printf("🧪 测试Destroy方法改进效果")

	// 1. 测试重复调用保护
	log.Printf("📋 测试1：重复调用保护测试")
	testDestroyIdempotent()

	time.Sleep(1 * time.Second)

	// 2. 测试并发调用安全性
	log.Printf("📋 测试2：并发调用安全性测试")
	testConcurrentDestroy()

	time.Sleep(1 * time.Second)

	// 3. 测试状态查询
	log.Printf("📋 测试3：状态查询测试")
	testDestroyStatus()

	time.Sleep(1 * time.Second)

	// 4. 测试资源清理顺序
	log.Printf("📋 测试4：资源清理顺序测试")
	testDestroyOrder()

	log.Printf("🎉 所有测试完成")
}

// 测试重复调用保护
func testDestroyIdempotent() {
	authState := &server.AuthState{
		UserID:       "test-user-idempotent",
		Token:        "test-token-idempotent",
		ClientID:     "test-client",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
	}

	// 启动上报
	log.Printf("🚀 启动上报...")
	authState.StartReporting(500 * time.Millisecond)

	if !authState.IsReporting() {
		log.Printf("❌ 启动失败")
		return
	}

	log.Printf("✅ 启动成功，等待1秒...")
	time.Sleep(1 * time.Second)

	// 检查初始状态
	if authState.IsDestroyed() {
		log.Printf("❌ 初始状态错误：应该未销毁")
		return
	}
	log.Printf("✅ 初始状态正确：未销毁")

	// 第一次销毁
	log.Printf("🗑️  第一次调用Destroy...")
	authState.Destroy()

	// 检查销毁状态
	if !authState.IsDestroyed() {
		log.Printf("❌ 第一次销毁失败")
		return
	}
	log.Printf("✅ 第一次销毁成功")

	// 重复销毁多次
	log.Printf("🔄 重复调用Destroy 5次...")
	for i := 0; i < 5; i++ {
		authState.Destroy()
		log.Printf("   第%d次重复调用完成", i+1)
	}

	// 检查最终状态
	if authState.IsDestroyed() {
		log.Printf("✅ 重复调用测试通过：状态保持一致")
	} else {
		log.Printf("❌ 重复调用测试失败：状态不一致")
	}
}

// 测试并发调用安全性
func testConcurrentDestroy() {
	authState := &server.AuthState{
		UserID:       "test-user-concurrent",
		Token:        "test-token-concurrent",
		ClientID:     "test-client",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
	}

	// 启动上报
	authState.StartReporting(200 * time.Millisecond)
	log.Printf("✅ 启动上报成功")

	var wg sync.WaitGroup
	destroyCount := 0
	successCount := 0

	// 并发调用Destroy 10次
	log.Printf("🔄 并发调用Destroy 10次...")
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			log.Printf("🗑️  Goroutine %d: 调用Destroy", id)
			authState.Destroy()
			destroyCount++
			
			// 检查状态
			if authState.IsDestroyed() {
				successCount++
				log.Printf("✅ Goroutine %d: Destroy调用完成，状态正确", id)
			} else {
				log.Printf("❌ Goroutine %d: Destroy调用完成，状态错误", id)
			}
		}(i)
	}

	wg.Wait()
	time.Sleep(100 * time.Millisecond)

	log.Printf("📊 并发调用测试结果:")
	log.Printf("   调用次数: 10")
	log.Printf("   完成次数: %d", destroyCount)
	log.Printf("   状态正确次数: %d", successCount)

	if authState.IsDestroyed() && successCount == 10 {
		log.Printf("✅ 并发调用测试通过：所有调用都安全完成")
	} else {
		log.Printf("❌ 并发调用测试失败")
	}
}

// 测试状态查询
func testDestroyStatus() {
	authState := &server.AuthState{
		UserID:       "test-user-status",
		Token:        "test-token-status",
		ClientID:     "test-client",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
	}

	// 初始状态
	if authState.IsDestroyed() {
		log.Printf("❌ 初始状态应该为未销毁")
	} else {
		log.Printf("✅ 初始状态正确：未销毁")
	}

	// 启动上报
	authState.StartReporting(1 * time.Second)
	if authState.IsReporting() {
		log.Printf("✅ 上报启动成功")
	}

	// 销毁后状态
	authState.Destroy()
	if authState.IsDestroyed() {
		log.Printf("✅ 销毁后状态正确：已销毁")
	} else {
		log.Printf("❌ 销毁后状态错误")
	}

	// 检查上报是否也停止了
	time.Sleep(100 * time.Millisecond)
	if !authState.IsReporting() {
		log.Printf("✅ 上报已正确停止")
	} else {
		log.Printf("❌ 上报未正确停止")
	}
}

// 测试资源清理顺序
func testDestroyOrder() {
	authState := &server.AuthState{
		UserID:       "test-user-order",
		Token:        "test-token-order",
		ClientID:     "test-client",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
	}

	// 启动上报
	log.Printf("🚀 启动上报...")
	authState.StartReporting(300 * time.Millisecond)

	if !authState.IsReporting() {
		log.Printf("❌ 启动失败")
		return
	}

	log.Printf("✅ 启动成功，等待1秒观察上报...")
	time.Sleep(1 * time.Second)

	// 销毁并观察顺序
	log.Printf("🗑️  开始销毁，观察资源清理顺序...")
	authState.Destroy()

	// 等待一下，确保异步操作完成
	time.Sleep(200 * time.Millisecond)

	if authState.IsDestroyed() && !authState.IsReporting() {
		log.Printf("✅ 资源清理顺序正确：先停止上报，再关闭连接")
	} else {
		log.Printf("❌ 资源清理顺序可能有问题")
	}
}
