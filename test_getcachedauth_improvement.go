package main

import (
	"log"
	"sync"
	"time"

	"socks5-quic-server/internal/server"
)

func main() {
	log.Printf("🧪 测试getCachedAuth销毁状态检查")

	// 1. 测试基本功能
	log.Printf("📋 测试1：基本功能测试")
	testBasicGetCachedAuth()

	time.Sleep(1 * time.Second)

	// 2. 测试销毁状态检查
	log.Printf("📋 测试2：销毁状态检查测试")
	testDestroyedStateCheck()

	time.Sleep(1 * time.Second)

	// 3. 测试并发安全性
	log.Printf("📋 测试3：并发安全性测试")
	testConcurrentGetCachedAuth()

	log.Printf("🎉 所有测试完成")
}

// 测试基本功能
func testBasicGetCachedAuth() {
	// 创建AuthManager
	authManager := server.NewAuthManager(nil)

	// 创建AuthState
	authState := &server.AuthState{
		UserID:       "test-user-basic",
		Token:        "test-token-basic",
		ClientID:     "test-client",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
	}

	// 手动添加到缓存（模拟cacheAuth的行为）
	authManager.GetAuthCache().Store(authState.Token, authState)

	// 测试获取
	cachedAuth := authManager.GetCachedAuthPublic(authState.Token)
	if cachedAuth != nil && cachedAuth.UserID == authState.UserID {
		log.Printf("✅ 基本功能测试通过：成功获取未销毁的AuthState")
	} else {
		log.Printf("❌ 基本功能测试失败：无法获取AuthState")
		return
	}

	// 测试不存在的token
	nonExistentAuth := authManager.GetCachedAuthPublic("non-existent-token")
	if nonExistentAuth == nil {
		log.Printf("✅ 不存在token测试通过：正确返回nil")
	} else {
		log.Printf("❌ 不存在token测试失败：应该返回nil")
	}
}

// 测试销毁状态检查
func testDestroyedStateCheck() {
	// 创建AuthManager
	authManager := server.NewAuthManager(nil)

	// 创建AuthState
	authState := &server.AuthState{
		UserID:       "test-user-destroyed",
		Token:        "test-token-destroyed",
		ClientID:     "test-client",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
	}

	// 启动上报
	authState.StartReporting(500 * time.Millisecond)

	// 手动添加到缓存
	authManager.GetAuthCache().Store(authState.Token, authState)

	// 验证未销毁时可以获取
	cachedAuth := authManager.GetCachedAuthPublic(authState.Token)
	if cachedAuth != nil {
		log.Printf("✅ 未销毁状态测试通过：成功获取AuthState")
	} else {
		log.Printf("❌ 未销毁状态测试失败：无法获取AuthState")
		return
	}

	// 销毁AuthState
	log.Printf("🗑️  销毁AuthState...")
	authState.Destroy()

	// 等待一下确保销毁完成
	time.Sleep(100 * time.Millisecond)

	// 验证销毁后无法获取
	cachedAuthAfterDestroy := authManager.GetCachedAuthPublic(authState.Token)
	if cachedAuthAfterDestroy == nil {
		log.Printf("✅ 销毁状态检查测试通过：已销毁的AuthState返回nil")
	} else {
		log.Printf("❌ 销毁状态检查测试失败：已销毁的AuthState仍然可以获取")
		return
	}

	// 验证缓存已被清理
	if _, exists := authManager.GetAuthCache().Load(authState.Token); !exists {
		log.Printf("✅ 缓存清理测试通过：已销毁的AuthState已从缓存中移除")
	} else {
		log.Printf("❌ 缓存清理测试失败：已销毁的AuthState仍在缓存中")
	}
}

// 测试并发安全性
func testConcurrentGetCachedAuth() {
	// 创建AuthManager
	authManager := server.NewAuthManager(nil)

	// 创建AuthState
	authState := &server.AuthState{
		UserID:       "test-user-concurrent",
		Token:        "test-token-concurrent",
		ClientID:     "test-client",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
	}

	// 启动上报
	authState.StartReporting(200 * time.Millisecond)

	// 手动添加到缓存
	authManager.GetAuthCache().Store(authState.Token, authState)

	var wg sync.WaitGroup
	getCount := 0
	nilCount := 0
	successCount := 0

	// 启动多个goroutine并发获取
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < 10; j++ {
				cachedAuth := authManager.GetCachedAuthPublic(authState.Token)
				getCount++
				if cachedAuth == nil {
					nilCount++
				} else {
					successCount++
				}
				time.Sleep(10 * time.Millisecond)
			}
		}(i)
	}

	// 在获取过程中销毁AuthState
	time.Sleep(100 * time.Millisecond)
	log.Printf("🗑️  在并发获取过程中销毁AuthState...")
	authState.Destroy()

	wg.Wait()

	log.Printf("📊 并发获取测试结果:")
	log.Printf("   总获取次数: %d", getCount)
	log.Printf("   返回nil次数: %d", nilCount)
	log.Printf("   返回成功次数: %d", successCount)

	if nilCount > 0 && successCount > 0 {
		log.Printf("✅ 并发安全性测试通过：销毁前后行为正确")
	} else if nilCount == getCount {
		log.Printf("✅ 并发安全性测试通过：销毁后全部返回nil")
	} else if successCount == getCount {
		log.Printf("⚠️  并发安全性测试：销毁可能发生在所有获取之后")
	} else {
		log.Printf("❌ 并发安全性测试失败：行为异常")
	}
}
