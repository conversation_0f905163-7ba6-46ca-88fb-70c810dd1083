package main

import (
	"log"
	"sync"
	"sync/atomic"
	"time"

	"socks5-quic-server/internal/server"
)

func main() {
	log.Printf("🧪 测试getCachedAuth竞态条件处理")

	// 测试竞态条件
	testRaceCondition()

	log.Printf("🎉 竞态条件测试完成")
}

// 测试竞态条件
func testRaceCondition() {
	// 创建AuthManager
	authManager := server.NewAuthManager(nil)

	// 创建AuthState
	authState := &server.AuthState{
		UserID:       "test-user-race",
		Token:        "test-token-race",
		ClientID:     "test-client",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
	}

	// 启动上报
	authState.StartReporting(100 * time.Millisecond)

	// 手动添加到缓存
	authManager.GetAuthCache().Store(authState.Token, authState)

	var wg sync.WaitGroup
	var getBeforeDestroy int64
	var getAfterDestroy int64
	var getNil int64
	var getSuccess int64

	// 启动多个goroutine持续获取
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < 100; j++ {
				cachedAuth := authManager.GetCachedAuthPublic(authState.Token)
				
				if authState.IsDestroyed() {
					atomic.AddInt64(&getAfterDestroy, 1)
				} else {
					atomic.AddInt64(&getBeforeDestroy, 1)
				}
				
				if cachedAuth == nil {
					atomic.AddInt64(&getNil, 1)
				} else {
					atomic.AddInt64(&getSuccess, 1)
				}
				
				// 短暂休眠，增加竞态条件发生的概率
				time.Sleep(1 * time.Millisecond)
			}
		}(i)
	}

	// 等待一段时间后销毁
	time.Sleep(50 * time.Millisecond)
	log.Printf("🗑️  开始销毁AuthState...")
	authState.Destroy()

	wg.Wait()

	log.Printf("📊 竞态条件测试结果:")
	log.Printf("   销毁前获取次数: %d", getBeforeDestroy)
	log.Printf("   销毁后获取次数: %d", getAfterDestroy)
	log.Printf("   返回nil次数: %d", getNil)
	log.Printf("   返回成功次数: %d", getSuccess)

	// 验证逻辑：销毁后的获取应该都返回nil
	if getAfterDestroy > 0 && getNil >= getAfterDestroy {
		log.Printf("✅ 竞态条件处理正确：销毁后的获取都返回nil")
	} else if getAfterDestroy == 0 {
		log.Printf("✅ 竞态条件测试：销毁发生在所有获取之后")
	} else {
		log.Printf("❌ 竞态条件处理可能有问题：销毁后仍有成功获取")
	}

	// 最终验证：确保缓存已清理
	finalAuth := authManager.GetCachedAuthPublic(authState.Token)
	if finalAuth == nil {
		log.Printf("✅ 最终状态正确：缓存已清理")
	} else {
		log.Printf("❌ 最终状态错误：缓存未清理")
	}
}
