package main

import (
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"golang.org/x/net/http2"
)

func main() {
	fmt.Println("🧪 开始HTTP2客户端认证测试...")
	fmt.Println("==================================")

	// HTTP2服务器地址
	serverAddr := "https://127.0.0.1:18443"
	fmt.Printf("🔍 目标HTTP2服务器: %s\n", serverAddr)

	// 创建HTTP2客户端
	client := createHTTP2Client()

	// 测试无效token
	fmt.Println("\n🔐 测试1: 无效token认证")
	testHTTP2Auth(client, serverAddr, "invalid-token-12345", "test-http2-client-1")

	fmt.Println("\n🔐 测试2: 空token认证")
	testHTTP2Auth(client, serverAddr, "", "test-http2-client-2")

	fmt.Println("\n🔐 测试3: 格式错误token认证")
	testHTTP2Auth(client, serverAddr, "malformed.token.format", "test-http2-client-3")

	fmt.Println("\n✅ 所有HTTP2测试完成！")
}

// createHTTP2Client 创建HTTP2客户端
func createHTTP2Client() *http.Client {
	// 创建TLS配置
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true,
		NextProtos:         []string{"h2"}, // 强制使用HTTP2
	}

	// 创建HTTP2传输
	transport := &http2.Transport{
		TLSClientConfig: tlsConfig,
	}

	return &http.Client{
		Transport: transport,
		Timeout:   10 * time.Second,
	}
}

// testHTTP2Auth 测试HTTP2认证
func testHTTP2Auth(client *http.Client, serverAddr, token, clientID string) {
	fmt.Printf("🔗 连接到: %s\n", serverAddr)
	fmt.Printf("📝 Token: %s\n", maskToken(token))
	fmt.Printf("🆔 Client ID: %s\n", clientID)

	// 根据HTTP2服务端实现，使用POST请求到/tunnel端点
	// 设置x-target和x-auth-token头部
	req, err := http.NewRequest("POST", serverAddr+"/tunnel", nil)
	if err != nil {
		fmt.Printf("❌ 创建请求失败: %v\n", err)
		return
	}

	// 设置HTTP2 SOCKS5协议要求的头部
	req.Header.Set("x-target", "httpbin.org:80") // 测试目标地址
	if token != "" {
		req.Header.Set("x-auth-token", token)
	}
	req.Header.Set("x-client-id", clientID)
	req.Header.Set("User-Agent", "SOCKS5-HTTP2-Test-Client/1.0")
	req.Header.Set("Content-Type", "application/octet-stream")

	// 发送请求
	fmt.Printf("📤 发送HTTP2认证请求...\n")
	start := time.Now()

	resp, err := client.Do(req)
	duration := time.Since(start)

	if err != nil {
		fmt.Printf("❌ 请求失败: %v\n", err)
		fmt.Printf("⏱️  耗时: %v\n", duration)
		return
	}
	defer resp.Body.Close()

	fmt.Printf("⏱️  请求耗时: %v\n", duration)
	fmt.Printf("📊 HTTP状态码: %d %s\n", resp.StatusCode, resp.Status)
	fmt.Printf("🌐 协议版本: %s\n", resp.Proto)

	// 检查响应头
	fmt.Println("📋 响应头信息:")
	for name, values := range resp.Header {
		for _, value := range values {
			fmt.Printf("   %s: %s\n", name, value)
		}
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ 读取响应体失败: %v\n", err)
	} else {
		bodyStr := string(body)
		if len(bodyStr) > 0 {
			fmt.Printf("📄 响应体: %s\n", bodyStr)
		} else {
			fmt.Printf("📄 响应体: (空)\n")
		}
	}

	// 分析认证结果
	analyzeHTTP2AuthResult(resp, string(body))
}

// analyzeHTTP2AuthResult 分析HTTP2认证结果
func analyzeHTTP2AuthResult(resp *http.Response, body string) {
	fmt.Println("🔍 认证结果分析:")

	switch resp.StatusCode {
	case 200:
		fmt.Println("   ✅ 认证成功 - 隧道建立")
	case 401:
		fmt.Println("   ❌ 认证失败 - 401 Unauthorized")

		// 检查认证失败的详细信息（根据HTTP2服务端实现）
		if authError := resp.Header.Get("X-Info"); authError != "" {
			fmt.Printf("   📄 认证错误信息(头部): %s\n", authError)
		}

		if wwwAuth := resp.Header.Get("WWW-Authenticate"); wwwAuth != "" {
			fmt.Printf("   🔐 认证方式: %s\n", wwwAuth)
		}

		if errorMsg := resp.Header.Get("X-Error-Message"); errorMsg != "" {
			fmt.Printf("   💬 错误消息(头部): %s\n", errorMsg)
		}

		// 检查响应体中的错误信息
		if body != "" {
			fmt.Printf("   📝 错误详情(响应体): %s\n", body)
		}

	case 400:
		fmt.Println("   ❌ 请求错误 - 400 Bad Request")
		if info := resp.Header.Get("X-Info"); info != "" {
			fmt.Printf("   💬 错误信息: %s\n", info)
		}
	case 403:
		fmt.Println("   ❌ 认证失败 - 403 Forbidden")
	case 502:
		fmt.Println("   ❌ 网关错误 - 502 Bad Gateway")
	case 500:
		fmt.Println("   ❌ 服务器内部错误 - 500 Internal Server Error")
	default:
		fmt.Printf("   ❓ 未知状态码: %d\n", resp.StatusCode)
	}

	// 检查是否是连接关闭错误
	if strings.Contains(body, "connection closed") ||
		strings.Contains(body, "Application error") {
		fmt.Println("   🚨 检测到连接关闭错误！这表明修复可能不完整")
	} else if resp.StatusCode == 401 {
		fmt.Println("   ✅ 正确接收到认证失败响应，没有连接关闭错误")
	}

	// 检查是否有认证失败的详细信息在头部
	if resp.StatusCode == 401 {
		if xInfo := resp.Header.Get("X-Info"); xInfo != "" {
			fmt.Printf("   🎯 **关键发现**: 认证失败消息在HTTP头部: %s\n", xInfo)
		} else {
			fmt.Println("   ⚠️  认证失败但未在X-Info头部找到详细信息")
		}
	}
}

// maskToken 隐藏token的敏感部分
func maskToken(token string) string {
	if token == "" {
		return "(空token)"
	}
	if len(token) <= 8 {
		return token[:len(token)/2] + "***"
	}
	return token[:4] + "***" + token[len(token)-4:]
}
