@echo off
chcp 65001 >nul

echo 🧪 开始真实API认证测试...
echo ======================================

REM 设置环境变量
set GOPROXY=https://goproxy.cn,direct
set GO111MODULE=on

echo 📡 测试API连接性...
go test -v -run TestAPIConnectivity ./test/ -timeout 30s

if %errorlevel% equ 0 (
    echo ✅ API连接性测试通过
) else (
    echo ❌ API连接性测试失败，请检查网络连接和API服务器状态
    exit /b 1
)

echo.
echo 🔐 开始完整的认证失败测试...
go test -v -run TestRealAPIAuthFailure ./test/ -timeout 60s

if %errorlevel% equ 0 (
    echo ✅ 真实API认证测试通过
    echo.
    echo 🎉 测试总结：
    echo    - API连接正常
    echo    - 认证失败处理正确
    echo    - 客户端能正确接收认证失败响应
    echo    - 连接关闭问题已修复
) else (
    echo ❌ 真实API认证测试失败
    exit /b 1
)

echo.
echo ✅ 所有测试完成！
pause 