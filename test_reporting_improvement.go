package main

import (
	"log"
	"sync"
	"time"

	"socks5-quic-server/internal/server"
)

func main() {
	log.Printf("🧪 测试StartReporting和StopReporting改进效果")

	// 1. 测试并发安全
	log.Printf("📋 测试1：并发安全测试")
	testConcurrentStartStop()

	time.Sleep(1 * time.Second)

	// 2. 测试资源管理
	log.Printf("📋 测试2：资源管理测试")
	testResourceManagement()

	time.Sleep(1 * time.Second)

	// 3. 测试状态查询
	log.Printf("📋 测试3：状态查询测试")
	testStatusQuery()

	log.Printf("🎉 所有测试完成")
}

// 测试并发安全
func testConcurrentStartStop() {
	authState := &server.AuthState{
		UserID:       "test-user-concurrent",
		Token:        "test-token-concurrent",
		ClientID:     "test-client",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
	}

	var wg sync.WaitGroup
	startCount := 0
	stopCount := 0

	// 并发启动10次
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			log.Printf("🔄 Goroutine %d: 尝试启动上报", id)
			authState.StartReporting(1 * time.Second)
			if authState.IsReporting() {
				startCount++
				log.Printf("✅ Goroutine %d: 启动成功", id)
			} else {
				log.Printf("⏭️  Goroutine %d: 已经启动，跳过", id)
			}
		}(i)
	}

	wg.Wait()

	if authState.IsReporting() {
		log.Printf("✅ 并发启动测试通过：最终状态为运行中")
	} else {
		log.Printf("❌ 并发启动测试失败：最终状态为停止")
	}

	// 等待一下，然后并发停止
	time.Sleep(500 * time.Millisecond)

	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			log.Printf("🛑 Goroutine %d: 尝试停止上报", id)
			authState.StopReporting()
			if !authState.IsReporting() {
				stopCount++
				log.Printf("✅ Goroutine %d: 停止成功", id)
			}
		}(i)
	}

	wg.Wait()
	time.Sleep(100 * time.Millisecond)

	if !authState.IsReporting() {
		log.Printf("✅ 并发停止测试通过：最终状态为停止")
	} else {
		log.Printf("❌ 并发停止测试失败：最终状态为运行中")
	}
}

// 测试资源管理
func testResourceManagement() {
	authState := &server.AuthState{
		UserID:       "test-user-resource",
		Token:        "test-token-resource",
		ClientID:     "test-client",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
	}

	// 启动上报
	log.Printf("🚀 启动上报...")
	authState.StartReporting(500 * time.Millisecond)

	if !authState.IsReporting() {
		log.Printf("❌ 启动失败")
		return
	}

	log.Printf("✅ 启动成功，等待2秒观察上报...")
	time.Sleep(2 * time.Second)

	// 停止上报
	log.Printf("🛑 停止上报...")
	authState.StopReporting()

	time.Sleep(100 * time.Millisecond)

	if authState.IsReporting() {
		log.Printf("❌ 停止失败")
	} else {
		log.Printf("✅ 停止成功")
	}

	// 尝试重复停止
	log.Printf("🔄 尝试重复停止...")
	authState.StopReporting()
	log.Printf("✅ 重复停止不会出错")

	// 尝试重新启动
	log.Printf("🔄 尝试重新启动...")
	authState.StartReporting(1 * time.Second)

	if authState.IsReporting() {
		log.Printf("✅ 重新启动成功")
		authState.StopReporting() // 清理
	} else {
		log.Printf("❌ 重新启动失败")
	}
}

// 测试状态查询
func testStatusQuery() {
	authState := &server.AuthState{
		UserID:       "test-user-status",
		Token:        "test-token-status",
		ClientID:     "test-client",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
	}

	// 初始状态
	if authState.IsReporting() {
		log.Printf("❌ 初始状态应该为停止")
	} else {
		log.Printf("✅ 初始状态正确：停止")
	}

	// 启动后状态
	authState.StartReporting(1 * time.Second)
	if authState.IsReporting() {
		log.Printf("✅ 启动后状态正确：运行中")
	} else {
		log.Printf("❌ 启动后状态错误")
	}

	// 停止后状态
	authState.StopReporting()
	time.Sleep(100 * time.Millisecond)
	if !authState.IsReporting() {
		log.Printf("✅ 停止后状态正确：停止")
	} else {
		log.Printf("❌ 停止后状态错误")
	}
}
