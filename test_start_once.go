package main

import (
	"log"
	"sync"
	"time"

	"socks5-quic-server/internal/server"
)

func main() {
	log.Printf("🧪 测试StartReporting是否需要startOnce保护")

	// 测试重复启动的问题
	testMultipleStarts()

	log.Printf("🎉 StartReporting测试完成")
}

func testMultipleStarts() {
	authState := &server.AuthState{
		UserID:       "test-user-start",
		Token:        "test-token-start",
		ClientID:     "test-client",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
	}

	log.Printf("📊 初始状态: IsReporting = %v", authState.IsReporting())

	// 并发启动多次
	log.Printf("🔄 并发启动StartReporting 10次...")

	var wg sync.WaitGroup
	startCount := 0

	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			log.Printf("🚀 Goroutine %d: 调用StartReporting", id)
			authState.StartReporting(500 * time.Millisecond)
			if authState.IsReporting() {
				startCount++
			}
			log.Printf("✅ Goroutine %d: StartReporting调用完成", id)
		}(i)
	}

	wg.Wait()
	time.Sleep(100 * time.Millisecond)

	log.Printf("📊 并发启动测试结果:")
	log.Printf("   调用次数: 10")
	log.Printf("   成功计数: %d", startCount)
	log.Printf("   最终状态: %v", authState.IsReporting())

	if authState.IsReporting() {
		log.Printf("✅ StartReporting并发调用正常，有保护机制")
	} else {
		log.Printf("❌ StartReporting并发调用异常")
	}

	// 测试停止后重新启动
	log.Printf("🛑 停止上报...")
	authState.StopReporting()
	time.Sleep(200 * time.Millisecond)

	log.Printf("📊 停止后状态: %v", authState.IsReporting())

	// 尝试重新启动
	log.Printf("🔄 尝试重新启动...")
	authState.StartReporting(1 * time.Second)

	if authState.IsReporting() {
		log.Printf("✅ 重新启动成功 - 这说明不需要startOnce")
		authState.StopReporting() // 清理
	} else {
		log.Printf("❌ 重新启动失败")
	}

	// 测试是否存在资源泄漏
	log.Printf("🔍 测试资源泄漏...")
	for i := 0; i < 5; i++ {
		authState.StartReporting(100 * time.Millisecond)
		time.Sleep(50 * time.Millisecond)
		log.Printf("   第%d次启动，状态: %v", i+1, authState.IsReporting())
	}

	authState.StopReporting()
	log.Printf("💡 如果看到多个goroutine启动，说明存在资源泄漏问题")
}
