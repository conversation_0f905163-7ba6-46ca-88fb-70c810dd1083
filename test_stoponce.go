package main

import (
	"log"
	"sync"
	"time"

	"socks5-quic-server/internal/server"
)

func main() {
	log.Printf("🧪 测试stopOnce设计的效果")

	// 测试重复调用StopReporting
	testStopOnce()

	log.Printf("🎉 stopOnce测试完成")
}

func testStopOnce() {
	authState := &server.AuthState{
		UserID:       "test-user-stoponce",
		Token:        "test-token-stoponce",
		ClientID:     "test-client",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
	}

	// 启动上报
	log.Printf("🚀 启动上报...")
	authState.StartReporting(500 * time.Millisecond)

	if !authState.IsReporting() {
		log.Printf("❌ 启动失败")
		return
	}

	log.Printf("✅ 启动成功，等待1秒观察上报...")
	time.Sleep(1 * time.Second)

	// 并发调用StopReporting多次
	log.Printf("🔄 并发调用StopReporting 10次...")

	var wg sync.WaitGroup
	stopCount := 0

	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			log.Printf("🛑 Goroutine %d: 调用StopReporting", id)
			authState.StopReporting()
			log.Printf("✅ Goroutine %d: StopReporting调用完成", id)
			stopCount++
		}(i)
	}

	wg.Wait()
	time.Sleep(200 * time.Millisecond) // 等待异步上报完成

	log.Printf("📊 测试结果:")
	log.Printf("   调用次数: %d", stopCount)
	log.Printf("   最终状态: %v", authState.IsReporting())

	if !authState.IsReporting() {
		log.Printf("✅ stopOnce工作正常：多次调用只执行一次，最终状态正确")
	} else {
		log.Printf("❌ stopOnce工作异常：状态不正确")
	}

	// 再次尝试调用StopReporting
	log.Printf("🔄 再次调用StopReporting...")
	authState.StopReporting()
	log.Printf("✅ 重复调用不会出错，stopOnce确保了幂等性")

	// 尝试重新启动（验证stopOnce不会影响重新启动）
	log.Printf("🔄 尝试重新启动上报...")
	authState.StartReporting(1 * time.Second)

	// 注意：这里会失败，因为stopOnce已经执行过了
	// 这实际上暴露了一个设计问题：stopOnce会阻止重新启动
	if authState.IsReporting() {
		log.Printf("✅ 重新启动成功")
		authState.StopReporting() // 清理
	} else {
		log.Printf("⚠️  重新启动失败 - 这是stopOnce设计的限制")
		log.Printf("💡 如果需要重新启动，可能需要重置stopOnce或使用不同的设计")
	}
}
