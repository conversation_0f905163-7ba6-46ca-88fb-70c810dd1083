package main

import (
	"fmt"
	"log"
	"sync/atomic"
	"time"

	"socks5-quic-server/internal/server"
)

func main() {
	log.Printf("🧪 开始测试流量事件处理机制")

	// 1. 创建TrafficManager
	trafficManager := server.NewTrafficManager(100)

	// 2. 创建测试用的AuthState（直接创建，不通过AuthManager）
	testToken := "test-token-123"
	testAuthState := &server.AuthState{
		Ip:           "127.0.0.1",
		UserID:       "test-user",
		Token:        testToken,
		ClientID:     "test-client",
		IsAuth:       true,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
		StreamCount:  0,
		BytesUp:      0,
		BytesDown:    0,
	}

	log.Printf("✅ 初始状态 - UserID: %s, Token: %s, BytesUp: %d, BytesDown: %d, LastActivity: %s",
		testAuthState.UserID, testAuthState.Token, testAuthState.BytesUp, testAuthState.BytesDown, testAuthState.LastActivity.Format("15:04:05"))

	// 3. 创建自定义的流量处理器来直接测试AuthState更新
	testHandler := &TestTrafficHandler{authState: testAuthState}
	trafficManager.RegisterHandler(testHandler)

	// 4. 模拟流量事件
	log.Printf("📊 开始模拟流量事件...")

	totalUpload := int64(0)
	totalDownload := int64(0)

	for i := 0; i < 5; i++ {
		uploaded := int64(1024 + i*512)
		downloaded := int64(2048 + i*1024)

		totalUpload += uploaded
		totalDownload += downloaded

		log.Printf("🔄 发送流量事件 #%d: 上传=%d, 下载=%d", i+1, uploaded, downloaded)

		// 发送流量事件
		trafficManager.UpdateTraffic(
			server.TrafficTypeQUIC,
			fmt.Sprintf("stream-%d", i),
			"test-conn-id",
			testToken,
			testAuthState.UserID,
			"example.com:80",
			uploaded,
			downloaded,
		)

		// 等待事件处理
		time.Sleep(100 * time.Millisecond)

		// 检查AuthState更新
		currentUp := atomic.LoadInt64(&testAuthState.BytesUp)
		currentDown := atomic.LoadInt64(&testAuthState.BytesDown)
		log.Printf("📈 当前统计 - BytesUp: %d, BytesDown: %d, LastActivity: %s",
			currentUp, currentDown, testAuthState.LastActivity.Format("15:04:05"))
	}

	// 5. 验证最终统计
	finalUp := atomic.LoadInt64(&testAuthState.BytesUp)
	finalDown := atomic.LoadInt64(&testAuthState.BytesDown)

	log.Printf("📊 最终统计验证:")
	log.Printf("   期望上传: %d, 实际上传: %d, 匹配: %v", totalUpload, finalUp, totalUpload == finalUp)
	log.Printf("   期望下载: %d, 实际下载: %d, 匹配: %v", totalDownload, finalDown, totalDownload == finalDown)

	if totalUpload == finalUp && totalDownload == finalDown {
		log.Printf("✅ 流量统计更新正确！")
	} else {
		log.Printf("❌ 流量统计更新有误！")
	}

	// 6. 测试LastActivity更新
	oldActivity := testAuthState.LastActivity
	time.Sleep(100 * time.Millisecond)

	// 再发送一个事件
	trafficManager.UpdateTraffic(
		server.TrafficTypeQUIC,
		"final-stream",
		"test-conn-id",
		testToken,
		testAuthState.UserID,
		"example.com:80",
		100,
		200,
	)

	time.Sleep(100 * time.Millisecond)
	newActivity := testAuthState.LastActivity

	if newActivity.After(oldActivity) {
		log.Printf("✅ LastActivity更新正确！旧时间: %s, 新时间: %s",
			oldActivity.Format("15:04:05.000"), newActivity.Format("15:04:05.000"))
	} else {
		log.Printf("❌ LastActivity更新有误！旧时间: %s, 新时间: %s",
			oldActivity.Format("15:04:05.000"), newActivity.Format("15:04:05.000"))
	}

	// 7. 清理资源
	trafficManager.Close()

	log.Printf("🎉 流量事件处理机制测试完成")
}

// TestTrafficHandler 测试用流量处理器
type TestTrafficHandler struct {
	authState *server.AuthState
}

func (h *TestTrafficHandler) HandleTraffic(event *server.TrafficEvent) {
	// 直接更新AuthState统计（模拟UserTrafficHandler的逻辑）
	atomic.AddInt64(&h.authState.BytesUp, event.Uploaded)
	atomic.AddInt64(&h.authState.BytesDown, event.Downloaded)
	h.authState.LastActivity = time.Now()

	log.Printf("🔧 TestHandler处理事件: 上传+%d, 下载+%d, Token=%s",
		event.Uploaded, event.Downloaded, event.AuthToken)
}

func (h *TestTrafficHandler) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"bytes_up":      atomic.LoadInt64(&h.authState.BytesUp),
		"bytes_down":    atomic.LoadInt64(&h.authState.BytesDown),
		"last_activity": h.authState.LastActivity.Unix(),
	}
}

func (h *TestTrafficHandler) GetName() string {
	return "test_handler"
}

func (h *TestTrafficHandler) Close() {
	// 无需特殊清理
}
